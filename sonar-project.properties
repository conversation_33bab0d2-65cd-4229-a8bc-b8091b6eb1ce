# must be unique in a given SonarQube instance
sonar.projectKey=itfacs-pmt-ccrecon-aggregation-accounting

# --- optional properties ---

sonar.java.source=17

sonar.java.jdkHome=${java.home}

sonar.java.libraries=target/

sonar.java.binaries=target/classes

sonar.plugins.downloadOnlyRequired=false

# exclusions
sonar.exclusions=src/test/**,src/main/resources/**,**/settings.xml,**/configurations/**,**/models/**,**/utils/**

# defaults to 'not provided'
sonar.projectVersion=v1.7.4