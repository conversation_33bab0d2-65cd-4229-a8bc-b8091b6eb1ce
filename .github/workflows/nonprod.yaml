name: Non-Prod - Build, Deploy

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment Environment'
        type: choice
        default: dev
        required: true
        options:
          - dev
          - stage

jobs:
  build-and-publish:
    name: Build and Publish Application
    uses: AAInternal/itfacs-payments-workflows/.github/workflows/ru_maven_docker.yml@v5
    permissions:
      id-token: write
      pull-requests: write
      contents: write
      issues: write
      security-events: write
      actions: read
    secrets: inherit
    with:
      applicationName: ${{ github.event.repository.name }}
      environment: ${{ github.event.inputs.environment }}
      javaVersion: 23
      runCodeCoverage: ${{ github.event.inputs.environment == 'dev' }}
      cronjob: false