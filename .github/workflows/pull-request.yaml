name: Pull Request Workflow - Draft & Review

on:
  pull_request:
    branches: [ "*" ]
    types: [converted_to_draft, opened, synchronize, ready_for_review, reopened, edited]
    paths-ignore:
      - "**/*.md"

jobs:
  pull-request-workflow:
    name: Pull Request Workflow
    uses: AAInternal/itfacs-payments-workflows/.github/workflows/ru_pull_request.yml@v5
    permissions:
      id-token: write
      pull-requests: write
      contents: write
      issues: write
      security-events: write
      actions: read
    secrets: inherit
    with:
      applicationName: ${{ github.event.repository.name }}
      javaVersion: 23