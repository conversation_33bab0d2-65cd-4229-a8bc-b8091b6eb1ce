# Webservice in a Docker Container

A Springboot Rest Webservice Project that can be deployed to a Docker container.

This is a simple Springboot application created at https://start.spring.io/ and then augmented with a few features:

* A single `greeting` REST json service accessible by GET request with optional `name` parameter.
* Available Open API docs for the service(s) automatically created and available as json or yaml
* Swagger UI page showing the available annotation driven docs and allowing test calls
## Azure Platform as a Service (PaaS)

You selected for extra Platform as a Service (PaaS) items from Azure. A pipeline was kicked off to create the storage account for Terraform state files.

**You will need to do the following for each selected PaaS service:**

1. Click on actions tab on the produced GitHub repo.
1. Run the `Terraform Plan` workflow for the services you selected for PaaS
1. After the `Terraform Plan` is executed you should be able to verify in the action logs that all resources are new resources.
    1. If the resource isn't new, be extremely careful. Terraform will delete resources if needed.
1. After verifying the output of `Terraform Plan` you can now run the `Terraform Apply` workflow.

## Build and Run Locally

Use Maven Wrapper

Build `./mvnw package` will create the jar in the target folder.

Run `./mvnw spring-boot:run` or `java -jar target/<jar file name>`

## Build in GitHub.com

Workflow files:

`.github/workflows/ci.yml` build the java artifact on any push/merge and any pull request.<br/>
`.github/workflows/scan.yml` perform SonarQube scan is done only for push/merge events on master/main branch OR on a
manual request. Note that you must create a repo level secret as described below for the scan to succeed.<br/>
`.github/workflows/release.yml` build the java artifact, run tests, build the docker image, publish to Artifactory, load
the image and do a simple test<br/>
`.github/workflows/promote.yml` perform SonarQube scan is done only for push/merge events on master/main branch OR on a
manual request

To enable SonarQube access you must create a repo level secret in the repo with the generated source code (this one, the
one for your application)
The easiest way to do this is create a secret named SONARQUBE_TOKEN containing a token created on your SonarQube account
on `sonarqube.aa.com`. You can also use any available token if you put its name in `scan.yml` in the
`with` section of the `sonarscan` action step, like this.

```yaml
   - name: sonarscan
     env:
       DEPLOYMENT_ID: "itfacs-pmt-ccrecon-aggregation-accounting"
     uses: ./.github/actions/sonarscan
     with:
       token: "${{ secrets.SONARQUBE_TOKEN }}"
```

## Rest Service

## Security

Out of the box, the service requires a valid JWT token issued by ping federate to operate, even when running locally.
To get a valid token:

Using your browser's developer tools, examine the headers sent in a request to developer.aa.com and copy the authorization header for PingFederate calls. For example if you navigate to the Orion Data Lake page and examine the [daeService/](https://developer.aa.com/api/deaService) call you should see the authorization header with the appropriate token. The authorization token will look like Authorization: Bearer lorem.ipsum.signature but you will only need to copy the lorem.ipsum.signature part. (the actual token will be much longer than lorem.ipsum.signature)

Press the authorize button in swagger ui and paste the token in there before invoking any service.

Set "authenticate=false" in application.properties to disable authentication

### HTML test endpoint

```
    https://hostname/
```

Exposes links to the various "information" pages under the API

----
####    Hello World

<u>Greeting: Hello World</u><br/>
<u>Greeting: Hello Friend</u><br/>
<u>User information</u><br/>
<u>Swagger UI Docs with testing</u><br/>
<u>Actuator Health</u><br/>
<u>Actuator Info</u><br/>
<u>Open API JSON</u><br/>
<u>Open API download YAML</u><br/>
----

### Endpoint:

```
    https://hostname/greeting
```

### Response:

```json
    {
      "id": 2,
      "content": "Hello, World!"
    }
```

### Health Check / Application Info

```
    https://hostname/actuator/health
    https://hostname/actuator/info
```

### Open API Docs

```
    https://host-name/docs
```

You can see the full Open API at:

```
    https://host-name/openapi.json - json format
    https://host-name/openapi.json.yaml - yaml format (download)
```

## API Spec

Currently, the api definition found in `catalog-info-api.yaml` refers to the `api.json` file found in the repo. In order to have the api definition use the one found in the kubernetes instance change the last line in `catalog-info-api.yaml` from:

```$text: ./api.json```

to 

```$text: https://itfacs-pmt-ccrecon-aggregation-accounting.drke.ok8s.aa.com/openapi.json```

For more information about how the catalog yaml file is structured, please refer to backstage's documentation: https://backstage.io/docs/features/software-catalog/descriptor-format#substitutions-in-the-descriptor-format

