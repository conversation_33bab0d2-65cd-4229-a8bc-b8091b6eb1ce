management:
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include:
          - health

  health:
    defaults:
      enabled: true
    redis:
      enabled: false
    binders:
      enabled: false
    status:
      http-mapping:
        DOWN: 503

springdoc:
  api-docs:
    path: /openapi.json
  swagger-ui:
    path: /docs
  show-actuator: true
  writer-with-default-pretty-printer: true


spring:
  cloud:
    azure:
      servicebus:
        namespace: ${CCRECON_SBQ_NAMESPACE}
        credential:
          client-id: ${CCRECON_SBQ_LISTEN_CLIENT_ID}
          client-secret: ${CCRECON_SBQ_LISTEN_CLIENT_SECRET}
        profile:
          tenant-id: ${AZURE_TENANT_ID}
    function:
      definition: consumeServiceBusDetailPaymentMessage;supplyServiceBusAggregatedTransactionMessage;supplyServiceBusException;
    stream:
      output-bindings: supplyServiceBusAggregatedTransactionMessage;supplyServiceBusException;
      input-bindings: consumeServiceBusDetailPaymentMessage
      bindings:
        consumeServiceBusDetailPaymentMessage-in-0:
          destination: ${DETAIL_PAYMENT_SERVICE_BUS_QUEUE_NAME}
          binder: servicebus
        supplyServiceBusAggregatedTransactionMessage-out-0:
          destination: ${ACCOUNTING_GL_QUEUE_NAME}
          binder: servicebus
        supplyServiceBusException-out-0:
          destination: ${ACCOUNTING_EXCEPTION_QUEUE_NAME}
          binder: servicebus
      servicebus:
        bindings:
          consumeServiceBusDetailPaymentMessage-in-0:
            consumer:
              auto-complete: false
          supplyServiceBusAggregatedTransactionMessage-out-0:
            producer:
              entity-type: queue
          supplyServiceBusException-out-0:
            producer:
              entity-type: queue
  datasource:
    url: ${C2_DB_URL}
    username: ${C2_DB_USERNAME}
    password: ${C2_DB_PASSWORD}
    driverClassName: org.postgresql.Driver
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    show-sql: true
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
  jackson:
    serialization:
      fail-on-empty-beans: false
  profiles:
    active: ${AGGREGATE_ACCOUNTING_PROFILE:default}

# set flag to true to enable authentication
authenticate: false

retry:
  delay: 1
  times: 2

ccrecon:
  aggregate:
    max-batch-size: ${AGGREGATE_ACCOUNTING_MAX_BATCH_SIZE} # should be a number


logging:
  level:
    com.aa.ccrecon.accounting.aggregation: ${LOG_LEVEL}