UATPAggregate_aggregateData1: |
  
  DROP TABLE IF EXISTS temp_detail_ds;
  
  CREATE TEMP TABLE temp_detail_ds AS
  SELECT
      detail_id,
      currency,
      converted_currency,
      transaction_type,
      payment_type,
      sales_source,
      processor,
      credit_account,
      debit_company_code,
      credit_company_code,
      line_item_text,
      cost_center,
      debit_account,
      post_to_ledger,
      pnr,
      ticket_number,
      amount,
      converted_amount,
      is_processed,
      transaction_date,
      created_timestamp,
      reference_id,
      recon_id,
      CONCAT(
          TRIM(debit_account),
          TRIM(credit_account),
          TRIM(debit_company_code),
          TRIM(credit_company_code),
          TRIM(currency),
          TRIM(converted_currency),
          TRIM(line_item_text),
          TRIM(recon_id),
          TRIM(post_to_ledger),
          TRIM(sales_source),
          TRIM(transaction_type)
      ) AS load_id
  FROM rec_account.receivable_accounting_trans_details
  WHERE is_processed = false
      AND payment_type IN ('TP', 'OA')
      AND post_to_ledger IS NOT NULL
      AND post_to_ledger <> ''
      AND created_timestamp <= CURRENT_TIMESTAMP
      AND sales_source IN (:sales_source)
      AND transaction_type IN ('SALE', 'REFUND');

UATPAggregate_aggregateData2: |
  DROP TABLE IF EXISTS temp_group_ids;
  
  CREATE TEMP TABLE temp_group_ids AS
  SELECT
      load_id,
      'UATP-' || NEXTVAL('rec_account.serial_aggregate_id') AS aggregate_id
  FROM temp_detail_ds
  GROUP BY load_id;

UATPAggregate_aggregateData3: |
  DROP TABLE IF EXISTS temp_backfill;
  
  CREATE TEMP TABLE temp_backfill AS
  SELECT
      d.*,
      a.aggregate_id
  FROM temp_detail_ds d
  JOIN temp_group_ids a ON d.load_id = a.load_id;

UATPAggregate_aggregateDataCount: |
  SELECT
      COUNT(*)
  FROM temp_backfill

UATPAggregate_assignGlobalId_1: |
  
  DROP TABLE IF EXISTS temp_ordered_details;
  
  CREATE TEMP TABLE temp_ordered_details AS
  
    WITH max_batch_number_cte AS (
        SELECT COALESCE(MAX(batch_number), 0) AS max_batch_number
        FROM rec_account.uatp_aggregate_receivable_accounting_trans
    ),  
  batch_size_vars AS (
      SELECT
          :maxBatchSize AS batch_size,
          (:maxBatchSize - 1) AS detail_records_per_batch
  ),
  aggregate_data AS (
      SELECT
          d.*,
          g.aggregate_id
      FROM temp_detail_ds d
      JOIN temp_group_ids g ON d.load_id = g.load_id
      WHERE d.is_processed = false
  ),
  numbered_data AS (
      SELECT
          ad.*,
          ROW_NUMBER() OVER (
              PARTITION BY ad.aggregate_id  -- Reset row numbers for each aggregate_id
              ORDER BY ad.transaction_type, ad.sales_source         
          ) AS row_num
      FROM aggregate_data ad
  ),
  batch_assignment AS (
      SELECT
          nd.*,
          FLOOR((nd.row_num - 1) / batch_size_vars.detail_records_per_batch) + 1 AS batch_offset
      FROM numbered_data nd
      CROSS JOIN batch_size_vars
  ),
  batches_with_aggregate_id AS (
      SELECT DISTINCT
          aggregate_id,
          batch_offset
      FROM batch_assignment
  ),
  batch_number_assignment AS (
      SELECT
          b.aggregate_id,
          b.batch_offset,
          ROW_NUMBER() OVER (
              ORDER BY b.aggregate_id, b.batch_offset
          ) + (SELECT max_batch_number FROM max_batch_number_cte) AS batch_number 
      FROM batches_with_aggregate_id b
  )
  
  SELECT
      ba.*,
      bn.batch_number AS batchwithinaggregate
  FROM batch_assignment ba
  JOIN batch_number_assignment bn ON ba.aggregate_id = bn.aggregate_id AND ba.batch_offset = bn.batch_offset;


UATPAggregate_assignGlobalId_2: |
  DROP TABLE IF EXISTS temp_aggregate_global_id;
  CREATE TEMP TABLE temp_aggregate_global_id AS
  SELECT
      aggregate_id,
      batchwithinaggregate,
      'UATP-' || NEXTVAL('rec_account.serial_global_id') AS globalid
  FROM (
      SELECT DISTINCT aggregate_id, batchwithinaggregate
      FROM temp_ordered_details
      ORDER BY aggregate_id, batchwithinaggregate
  ) unique_combinations;

UATPAggregate_assignGlobalId_3: |
  DROP TABLE IF EXISTS temp_Final_Global_ID_Assignment;
  
  CREATE TEMP TABLE temp_Final_Global_ID_Assignment AS
  SELECT
      od.*,
      agid.globalid AS globalid
  FROM temp_ordered_details od
  JOIN temp_aggregate_global_id agid ON od.aggregate_id = agid.aggregate_id
      AND od.batchwithinaggregate = agid.batchwithinaggregate;

UATPAggregate_assignGlobalId_4: |
  DROP TABLE IF EXISTS temp_aggregated_amount;
  
  CREATE TEMP TABLE temp_aggregated_amount AS
  SELECT
      aggregate_id,
      batchwithinaggregate,
      SUM(amount) AS total_amount,
      SUM(converted_amount) AS total_converted_amount,
      recon_id,
      debit_account,
      credit_account,
      debit_company_code,
      credit_company_code,
      currency,
      converted_currency,
      post_to_ledger,
      line_item_text,
      globalid,
      sales_source,
      transaction_type
  FROM temp_Final_Global_ID_Assignment
  GROUP BY
      aggregate_id,
      batchwithinaggregate,
      globalid,
      recon_id,
      debit_account,
      credit_account,
      debit_company_code,
      credit_company_code,
      currency,
      converted_currency,
      post_to_ledger,
      line_item_text,
      sales_source,
      transaction_type;

UATPAggregate_assignGlobalId_5: |
  DROP TABLE IF EXISTS temp_sequential_ids;
  
  CREATE TEMP TABLE temp_sequential_ids AS
  SELECT
      detail_id,
      recon_id,
      debit_account,
      credit_account,
      debit_company_code,
      credit_company_code,
      post_to_ledger,
      currency,
      converted_currency,
      line_item_text,
      amount,
      converted_amount,
      'DETAIL' AS record_type,
      load_id,
      aggregate_id,
      globalid,
      ROW_NUMBER() OVER (
          PARTITION BY aggregate_id
          ORDER BY currency, converted_currency, debit_account, credit_account, debit_company_code, credit_company_code, post_to_ledger, line_item_text, amount DESC
      ) AS seq_num,
      batchwithinaggregate,
      sales_source,
      transaction_type
  FROM temp_Final_Global_ID_Assignment
  
  UNION ALL
  
  SELECT
      NULL AS detail_id,
      recon_id,
      debit_account,
      credit_account,
      debit_company_code,
      credit_company_code,
      post_to_ledger,
      currency,
      converted_currency,
      line_item_text,
      total_amount AS amount,
      total_converted_amount AS converted_amount,
      'AGGREGATE' AS record_type,
      NULL AS load_id,
      aggregate_id,
      globalid,
      999999 AS seq_num,
      batchwithinaggregate,
      sales_source,
      transaction_type
  FROM temp_aggregated_amount;


UATPAggregate_getGlobalIds: |
  SELECT
      DISTINCT globalid
  FROM temp_sequential_ids
  ORDER BY globalid ASC;

UATPAggregate_insert_CreateTempTable: |
  DROP TABLE IF EXISTS temp_ordered_combined;
  
  CREATE TEMP TABLE temp_ordered_combined AS
  SELECT *,
      ROW_NUMBER() OVER (
          ORDER BY credit_company_code, debit_company_code, currency, globalid, seq_num
      ) AS assigned_id
  FROM temp_sequential_ids;

UATPAggregate_insert_InsertAccountingRecords: |
  INSERT INTO rec_account.uatp_aggregate_receivable_accounting_trans (
      detail_id,
      sort_order,
      aggregate_id,
      recon_id,
      debit_account,
      credit_account,
      debit_company_code,
      credit_company_code,
      currency,
      converted_currency,
      line_item_text,
      amount,
      converted_amount,
      record_type,
      post_to_ledger,
      global_id,
      batch_number,
      sales_source,
      transaction_type
  )
  SELECT
      detail_id,
      assigned_id AS sort_order,
      aggregate_id,
      recon_id,
      debit_account,
      credit_account,
      debit_company_code,
      credit_company_code,
      currency,
      converted_currency,
      line_item_text,
      amount,
      converted_amount,
      record_type,
      post_to_ledger,
      globalid,
      batchwithinaggregate,
      sales_source,
      transaction_type
  FROM temp_ordered_combined;

UATPAggregate_insert_UpdateProcessedFlag: |
  UPDATE rec_account.receivable_accounting_trans_details a
  SET
      aggregate_id = b.aggregate_id,
      is_processed = TRUE
  FROM temp_backfill b
  WHERE a.detail_id = b.detail_id;

