VerifyAvailableAggregate: |
  select count(*) 
      FROM rec_account.receivable_accounting_trans_details 
      WHERE is_processed = FALSE 
      AND post_to_ledger IS NOT NULL AND post_to_ledger <> '' 
      AND payment_type NOT IN ('TP', 'OA') 
      AND created_timestamp <= CURRENT_TIMESTAMP  

ImportAggregate: |
  WITH
  detail_ds AS (
  SELECT
  detail_id,
  currency,
  converted_currency,
  converted_amount,
  transaction_type,
  payment_type,
  sales_source,
  processor,
  credit_account,
  debit_company_code,
  credit_company_code,
  line_item_text,
  cost_center,
  debit_account,
  post_to_ledger,
  pnr,
  ticket_number,
  amount,
  is_processed,
  transaction_date,
  created_timestamp,
  reference_id,
  recon_id,
  CONCAT(TRIM(debit_account), TRIM(credit_account), TRIM(converted_currency), TRIM(debit_company_code), TRIM(credit_company_code), 
  TRIM(currency), TRIM(line_item_text), TRIM(recon_id), TRIM(post_to_ledger), TRIM(sales_source), 
  TRIM(transaction_type)) AS load_id 
  FROM rec_account.receivable_accounting_trans_details
  WHERE is_processed = false
  AND sales_source IN (:sales_source)
  and transaction_type in ('SALE', 'REFUND')
  AND post_to_ledger IS NOT NULL
  AND post_to_ledger <> ''
  AND payment_type NOT IN ('TP', 'OA')
  AND created_timestamp <= CURRENT_TIMESTAMP
  ),

  aggregated AS (
  SELECT
  NEXTVAL('rec_account.serial_aggregate_id') AS aggregate_id,
  recon_id,
  debit_account,
  credit_account,
  debit_company_code,
  credit_company_code,
  post_to_ledger,
  currency,
  converted_currency,
  line_item_text,
  SUM(amount) AS sub_total,
  SUM(converted_amount) as converted_subtotal,
  load_id,
  sales_source,
  transaction_type
  FROM detail_ds
  GROUP BY recon_id, debit_account, credit_account, debit_company_code, credit_company_code, currency, 
  converted_currency, line_item_text, load_id, post_to_ledger, sales_source, transaction_type
  ),

  backfill AS (
  SELECT
  a.detail_id,
  a.recon_id,
  b.aggregate_id
  FROM detail_ds a
  JOIN aggregated b ON a.load_id = b.load_id
  ),

  insert_agg AS (
  INSERT INTO rec_account.aggregate_receivable_accounting_trans
  (aggregate_id, recon_id, debit_account, credit_account, debit_company_code, credit_company_code, currency, 
  converted_currency, line_item_text, sub_total, converted_subtotal_amount, post_to_ledger, sales_source, transaction_type)
  SELECT
  aggregate_id,
  recon_id,
  debit_account,
  credit_account,
  debit_company_code,
  credit_company_code,
  currency,
  converted_currency,
  line_item_text,
  sub_total,
  converted_subtotal,
  post_to_ledger,
  sales_source,
  transaction_type
  FROM aggregated
  ),

  update_details AS (
  UPDATE rec_account.receivable_accounting_trans_details a
  SET aggregate_id = b.aggregate_id,
  is_processed = true
  FROM backfill b
  WHERE a.detail_id = b.detail_id
  )
  
  SELECT count(*) from aggregated;


AssignGlobalId: |
  
  WITH last_batch_number AS (
    SELECT sales_source, transaction_type, MAX(batch_number) AS last_batch
    FROM rec_account.aggregate_receivable_accounting_trans
    WHERE sales_source IN (:sales_source)
    and transaction_type in ('SALE', 'REFUND')
    GROUP BY transaction_type, sales_source
  ),


  countid AS (
    SELECT transaction_type, sales_source,
    CEIL((CAST(COUNT(*) AS DECIMAL(38,2)) / (:maxBatchSize / 2) )) AS number_of_batches  
    FROM rec_account.aggregate_receivable_accounting_trans
    WHERE is_processed = false
    AND global_id IS NULL
    AND sales_source IN (:sales_source)
    and transaction_type in ('SALE', 'REFUND')
    GROUP BY transaction_type, sales_source
  ),

  batch AS (
  SELECT
  NTILE(CAST(number_of_batches AS int)) OVER (PARTITION BY a.transaction_type, a.sales_source ORDER BY recon_id, a.currency, a.converted_currency) 
                  + COALESCE(l.last_batch, 0) AS batch_number,
  a.aggregate_id,
  a.recon_id,
  a.debit_account,
  a.credit_account,
  a.debit_company_code,
  a.credit_company_code,
  a.line_item_text,
  a.currency,
  a.converted_currency,
  a.sub_total,
  a.converted_subtotal_amount,
  a.created_timestamp,
  a.post_to_ledger,
  a.sales_source,
  a.transaction_type
  FROM rec_account.aggregate_receivable_accounting_trans a
  JOIN countid c ON a.sales_source = c.sales_source
  and a.transaction_type = c.transaction_type
  LEFT JOIN last_batch_number l ON a.sales_source = l.sales_source
  and a.transaction_type = l.transaction_type
  WHERE a.is_processed = false
  AND a.global_id IS NULL
  AND a.sales_source IN (:sales_source)
  and a.transaction_type in ('SALE', 'REFUND')
  ORDER BY a.currency, a.converted_currency
  ),

  globalid AS (
  SELECT DISTINCT batch_number, sales_source,transaction_type
  FROM batch
  ),
  
  distinctglobalid AS(
  SELECT *,
  NEXTVAL('rec_account.serial_global_id') AS global_id 
  FROM globalid
  ),

  saploader AS (
    SELECT
    a.batch_number,
    a.aggregate_id,
    a.recon_id,
    a.debit_account,
    a.credit_account,
    a.debit_company_code,
    a.credit_company_code,
    a.line_item_text,
    a.post_to_ledger,
    a.currency,
    a.converted_currency,
    a.sub_total,
    a.converted_subtotal_amount,
    a.created_timestamp,
    b.global_id,
    a.sales_source,
    a.transaction_type
    FROM batch a
    JOIN distinctglobalid b ON a.batch_number = b.batch_number AND a.sales_source = b.sales_source and a.transaction_type = b.transaction_type 
  ),

  updateagg AS (
  UPDATE rec_account.aggregate_receivable_accounting_trans a
  SET global_id = b.global_id,
  batch_number = b.batch_number
  FROM saploader b
  WHERE a.aggregate_id = b.aggregate_id
  )
  
  SELECT 1;
  
  
  
  
