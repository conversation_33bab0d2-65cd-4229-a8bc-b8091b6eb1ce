package com.aa.ccrecon.accounting.aggregation.service;

import com.aa.ccrecon.accounting.aggregation.factory.MaskingLoggerFactory;
import com.aa.ccrecon.accounting.aggregation.logger.MaskingLogger;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Sinks;

@Component
public class MessageSenderService {

	private static final MaskingLogger log = MaskingLoggerFactory.getLogger(MessageSenderService.class);


	private final Sinks.Many<Message<AggregatedTransaction>> serviceBusAggregatedTransactionSink;

	private final Sinks.Many<Message<AggregatedTransaction>> serviceBusExceptionSink;

	public MessageSenderService(@Qualifier(value = "aggregatedTransactionSink") Sinks.Many<Message<AggregatedTransaction>> serviceBusAggregatedTransactionSink,
								@Qualifier(value = "exceptionSink") Sinks.Many<Message<AggregatedTransaction>> serviceBusExceptionSink) {
		this.serviceBusAggregatedTransactionSink = serviceBusAggregatedTransactionSink;
		this.serviceBusExceptionSink = serviceBusExceptionSink;
	}

	public void sendMessageToGlQueue(AggregatedTransaction aggregatedTransaction) {
		log.debug("Going to add message to Sinks.Many. Message: '{}'", aggregatedTransaction);
		serviceBusAggregatedTransactionSink.emitNext(MessageBuilder.withPayload(aggregatedTransaction).build(),
				Sinks.EmitFailureHandler.FAIL_FAST);
	}

	public void sendMessageToExceptionQueue(AggregatedTransaction aggregatedTransaction) {
		log.debug("Going to add message to Sinks.Many. Message: '{}'", aggregatedTransaction);
		serviceBusExceptionSink.emitNext(MessageBuilder.withPayload(aggregatedTransaction).build(),
				Sinks.EmitFailureHandler.FAIL_FAST);
	}

}