package com.aa.ccrecon.accounting.aggregation.processors;

import com.aa.ccrecon.accounting.aggregation.factory.MaskingLoggerFactory;
import com.aa.ccrecon.accounting.aggregation.logger.MaskingLogger;
import com.aa.ccrecon.accounting.aggregation.utils.YamlPropertySourceFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@PropertySource(value = "classpath:sql/NON_UATPAggregateQuery.yaml", factory = YamlPropertySourceFactory.class)
public class VerifyAvailableAggregateExecutor extends AbstractExecutor {

    private static final MaskingLogger log = MaskingLoggerFactory.getLogger(VerifyAvailableAggregateExecutor.class);

    @Value("${VerifyAvailableAggregate}")
    private String sql;

    final JdbcTemplate jdbcTemplate;

    @Override
    public void execute(AggregateRequest request) {

        try {
            Integer numRows = jdbcTemplate.queryForObject(sql, Integer.class);
            log.debug("Found rows to aggregate {}", numRows);
        } catch (EmptyResultDataAccessException erd) {
            log.debug("No rows found to integrate");
        }

        super.execute(request);
    }
}
