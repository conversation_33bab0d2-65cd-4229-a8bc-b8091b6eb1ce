package com.aa.ccrecon.accounting.aggregation.service;

import java.util.List;

import com.aa.ccrecon.accounting.aggregation.factory.AggregatedTransactionRepositoryFactory;
import com.aa.ccrecon.accounting.aggregation.factory.AggregatedTransactionRepositoryFactory.RepositoryType;
import com.aa.ccrecon.accounting.aggregation.repository.AggregateReceivableAccountingTransRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import com.aa.ccrecon.accounting.aggregation.mapper.AggregateTransactionProjection;

@Service
@RequiredArgsConstructor
public class AggregateTransactionService {

	private final RetryService retryService;

	private final AggregatedTransactionRepositoryFactory aggregatedTransactionRepositoryFactory;

	public List<AggregateTransactionProjection> findUnprocessedAggregatedTransactions(RepositoryType repositoryType) {
		AggregateReceivableAccountingTransRepository aggregateReceivableAccountingTransRepository = aggregatedTransactionRepositoryFactory.getAggregatedTransactionRepository(repositoryType);
		return retryService.retry(aggregateReceivableAccountingTransRepository::findUnprocessedAggregatedTransactions);
	}

	public Integer updateAggregatedTransactionsWithGlobalIdAndIsProcessedFalse(RepositoryType repositoryType, String globalId) {
		AggregateReceivableAccountingTransRepository aggregateReceivableAccountingTransRepository = aggregatedTransactionRepositoryFactory.getAggregatedTransactionRepository(repositoryType);
		return retryService.retry(() -> aggregateReceivableAccountingTransRepository
				.updateAggregatedTransactionsWithGlobalIdAndIsProcessedFalse(globalId));
	}

}
