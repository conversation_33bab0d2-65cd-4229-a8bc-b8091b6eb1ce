package com.aa.ccrecon.accounting.aggregation.service;

import com.aa.ccrecon.accounting.aggregation.factory.MaskingLoggerFactory;
import com.aa.ccrecon.accounting.aggregation.logger.MaskingLogger;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.function.Supplier;


@Service
public class   RetryService {

    private static final MaskingLogger log = MaskingLoggerFactory.getLogger(RetryService.class);

    @Retryable(maxAttemptsExpression = "${retry.times}",
            backoff = @Backoff(delayExpression = "${retry.delay}"))
    public <T> T retry(Supplier<T> function) {
        return function.get();

    }

    @Retryable(maxAttemptsExpression = "${retry.times}",
            backoff = @Backoff(delayExpression = "${retry.delay}"))
    public void retry(Runnable runnable) {
        runnable.run();
    }

    @Recover
    public <T> T catchError(RuntimeException e, Supplier<T> function){
        log.error("Exception occurred: {} - {}", e.getClass().getSimpleName(), e.getMessage());
        throw e;
    }

    @Recover
    public void catchError(RuntimeException e){
        log.error("Exception occurred: {} - {}", e.getClass().getSimpleName(), e.getMessage());
        throw e;
    }
}