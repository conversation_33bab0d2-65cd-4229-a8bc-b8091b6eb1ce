package com.aa.ccrecon.accounting.aggregation.utils;

import org.springframework.beans.factory.annotation.Value;

import java.util.List;

public class AppConstants {

    private AppConstants() {
    }

    public static final String HEADER_VERSION = "1.0.0";
    public static final String SOURCE_ID = "itfacs-pmt-ccrecon-aggregation-accounting";
    public static final String APPLICATION_ID = "ccrecon";
    public static final String AGGREGATION_ACCOUNTING_FAILED = "AGGREGATION_ACCOUNTING_FAILED";
    public static final String EXCEPTION_STATUS = "OPEN";



}
