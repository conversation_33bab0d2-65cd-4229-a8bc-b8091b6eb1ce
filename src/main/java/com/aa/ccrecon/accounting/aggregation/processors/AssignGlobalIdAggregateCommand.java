package com.aa.ccrecon.accounting.aggregation.processors;

import com.aa.ccrecon.accounting.aggregation.config.AppConfig;
import com.aa.ccrecon.accounting.aggregation.factory.MaskingLoggerFactory;
import com.aa.ccrecon.accounting.aggregation.logger.MaskingLogger;
import com.aa.ccrecon.accounting.aggregation.mapper.IntegerResultRowMapper;
import com.aa.ccrecon.accounting.aggregation.utils.YamlPropertySourceFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import java.sql.Types;

@Component
@RequiredArgsConstructor
@PropertySource(value= "classpath:sql/NON_UATPAggregateQuery.yaml", factory = YamlPropertySourceFactory.class)
public class AssignGlobalIdAggregateCommand extends AbstractExecutor
{
	@Value("${AssignGlobalId}")
	private String sql;

	private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

	private final AppConfig appConfig;

	private static final MaskingLogger log = MaskingLoggerFactory.getLogger(AssignGlobalIdAggregateCommand.class);

	@Override
	public void execute(AggregateRequest request) {

		MapSqlParameterSource params = new MapSqlParameterSource()
				.addValue("maxBatchSize", appConfig.getMaxBatchSize(), Types.DOUBLE)
				.addValue("sales_source", appConfig.getSalesSource());

		log.debug("Starting to assign global ids");
		namedParameterJdbcTemplate.query(sql, params, new IntegerResultRowMapper());
		log.debug("Finished assigning global ids");

		super.execute(request);
	}

}
