package com.aa.ccrecon.accounting.aggregation.processors;


import lombok.Setter;
import org.springframework.transaction.annotation.Transactional;

@Setter
public abstract class AbstractExecutor {

    private AbstractExecutor nextProcessor;

    @Transactional
    public void execute(AggregateRequest request) {
        if (nextProcessor != null)
            this.nextProcessor.execute(request);
    }

}
