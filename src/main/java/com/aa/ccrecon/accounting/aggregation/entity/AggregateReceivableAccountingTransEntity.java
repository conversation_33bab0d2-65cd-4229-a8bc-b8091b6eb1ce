package com.aa.ccrecon.accounting.aggregation.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;

@MappedSuperclass
public class AggregateReceivableAccountingTransEntity {

    @Id
    @Column(name = "aggregate_id")
    protected String aggregateId;

    @Column(name = "is_processed")
    protected Boolean isProcessed;

    @Column(name = "global_id")
    protected String globalId;

    @Column(name = "batch_number")
    protected Integer batchNumber;

    @Column(name = "sales_source")
    protected String salesSource;

    protected String transactionType;
}
