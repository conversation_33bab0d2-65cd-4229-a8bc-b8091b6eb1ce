package com.aa.ccrecon.accounting.aggregation.processors.uatp;

import com.aa.ccrecon.accounting.aggregation.config.AppConfig;
import com.aa.ccrecon.accounting.aggregation.factory.MaskingLoggerFactory;
import com.aa.ccrecon.accounting.aggregation.logger.MaskingLogger;
import com.aa.ccrecon.accounting.aggregation.processors.AbstractExecutor;
import com.aa.ccrecon.accounting.aggregation.processors.AggregateRequest;
import com.aa.ccrecon.accounting.aggregation.utils.YamlPropertySourceFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@PropertySource(value = "classpath:/sql/UATPAggregateQueries.yaml", factory = YamlPropertySourceFactory.class)
public class UATPAggregateDataCommand extends AbstractExecutor
{
    @Value("${UATPAggregate_aggregateData1}")
    private String aggregateData1;
    @Value("${UATPAggregate_aggregateData2}")
    private String aggregateData2;
    @Value("${UATPAggregate_aggregateData3}")
    private String aggregateData3;
    @Value("${UATPAggregate_aggregateDataCount}")
    private String aggregateDataCount;

    final JdbcTemplate jdbcTemplate;

    final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    final AppConfig appConfig;

    private static final MaskingLogger log = MaskingLoggerFactory.getLogger(UATPAggregateDataCommand.class);

    @Override
    public void execute (AggregateRequest request) {

        try {
            log.debug("Starting UATP data aggregation..");

            MapSqlParameterSource params = new MapSqlParameterSource()
                    .addValue("sales_source", appConfig.getSalesSource());

            namedParameterJdbcTemplate.update(aggregateData1, params);
            jdbcTemplate.execute(aggregateData2);
            jdbcTemplate.execute(aggregateData3);
            Integer numRows = jdbcTemplate.queryForObject(aggregateDataCount, Integer.class);

            log.debug("UATP records selected and assigned Aggregate ID's: {}", numRows);
        }
        catch (DataAccessException dae) {
            log.error("Assignment of Aggregate ID's failed (Step 1-3)", dae);
            throw dae;
        }

        super.execute(request);
    }

}
