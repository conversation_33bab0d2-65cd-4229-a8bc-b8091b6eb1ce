package com.aa.ccrecon.accounting.aggregation.processors.uatp;

import com.aa.ccrecon.accounting.aggregation.factory.MaskingLoggerFactory;
import com.aa.ccrecon.accounting.aggregation.logger.MaskingLogger;
import com.aa.ccrecon.accounting.aggregation.processors.AbstractExecutor;
import com.aa.ccrecon.accounting.aggregation.processors.AggregateRequest;
import com.aa.ccrecon.accounting.aggregation.utils.YamlPropertySourceFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@PropertySource(value = "classpath:/sql/UATPAggregateQueries.yaml", factory = YamlPropertySourceFactory.class)
public class UATPCreationCommand extends AbstractExecutor
{

    @Value("${UATPAggregate_insert_CreateTempTable}")
    private String insertCommand1;
    @Value("${UATPAggregate_insert_InsertAccountingRecords}")
    private String insertCommand2;
    @Value("${UATPAggregate_insert_UpdateProcessedFlag}")
    private String insertCommand3;

    final JdbcTemplate jdbcTemplate;

    private static final MaskingLogger log = MaskingLoggerFactory.getLogger(UATPCreationCommand.class);

    @Override
    public void execute (AggregateRequest request) {
        try {
            jdbcTemplate.execute(insertCommand1);
            jdbcTemplate.execute(insertCommand2);
            jdbcTemplate.execute(insertCommand3);
        } catch(DataAccessException dae) {
            log.error("Failed to insert records in UATP aggregate table (Step 9-11)", dae);
            throw dae;
        }
        super.execute(request);
    }

}
