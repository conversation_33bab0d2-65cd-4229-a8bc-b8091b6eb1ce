package com.aa.ccrecon.accounting.aggregation.repository;

import com.aa.ccrecon.accounting.aggregation.entity.AggregateGlUatpAggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.accounting.aggregation.mapper.AggregateTransactionProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface AggregateGlUatpReceivableAccountingTransRepository extends AggregateReceivableAccountingTransRepository, JpaRepository<AggregateGlUatpAggregateReceivableAccountingTransEntity, String> {
	
	@Query("SELECT a.globalId globalId, a.salesSource salesSource, COUNT(a.batchNumber) count, 'AGGREGATED_GL_UATP' as messageType, a.transactionType as transactionType "
			+ "FROM AggregateGlUatpAggregateReceivableAccountingTransEntity a "
			+ "WHERE a.isProcessed = false GROUP BY a.globalId, a.salesSource, a.transactionType ORDER BY a.globalId")
	List<AggregateTransactionProjection> findUnprocessedAggregatedTransactions();

	@Modifying
	@Transactional
	@Query("UPDATE AggregateGlUatpAggregateReceivableAccountingTransEntity a SET a.isProcessed = true WHERE a.globalId = :globalId")
	Integer updateAggregatedTransactionsWithGlobalIdAndIsProcessedFalse(@Param("globalId") String globalId);

}
