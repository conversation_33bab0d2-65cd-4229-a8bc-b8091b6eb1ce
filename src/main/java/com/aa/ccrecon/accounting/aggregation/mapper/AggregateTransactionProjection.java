package com.aa.ccrecon.accounting.aggregation.mapper;

import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.constants.SalesSource;
import com.aa.ccrecon.domain.subledger.TransactionType;

public interface AggregateTransactionProjection {

	String getGlobalId();
	
	Long getCount();

	MessageType getMessageType();

	SalesSource getSalesSource();

	TransactionType.TYPES getTransactionType();
}
