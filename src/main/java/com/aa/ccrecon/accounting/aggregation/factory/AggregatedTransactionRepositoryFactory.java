package com.aa.ccrecon.accounting.aggregation.factory;

import com.aa.ccrecon.accounting.aggregation.repository.AggregateReceivableAccountingTransRepository;
import com.aa.ccrecon.accounting.aggregation.repository.AggregateGlReceivableAccountingTransRepository;
import com.aa.ccrecon.accounting.aggregation.repository.AggregateGlUatpReceivableAccountingTransRepository;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

@Component
public class AggregatedTransactionRepositoryFactory {

    public enum RepositoryType {
        AGGREGATE,
        UATP_AGGREGATE
    }

    private final Map<RepositoryType, AggregateReceivableAccountingTransRepository> aggregatedTransactionRepositories = new EnumMap<>(RepositoryType.class);

    public AggregatedTransactionRepositoryFactory(AggregateGlReceivableAccountingTransRepository aggregateGlUatpAggregatedTransactionRepository,
                                                  AggregateGlUatpReceivableAccountingTransRepository uatpAggregatedAggregatedTransactionRepository) {
        aggregatedTransactionRepositories.put(RepositoryType.AGGREGATE, aggregateGlUatpAggregatedTransactionRepository);
        aggregatedTransactionRepositories.put(RepositoryType.UATP_AGGREGATE, uatpAggregatedAggregatedTransactionRepository);
    }

    public AggregateReceivableAccountingTransRepository getAggregatedTransactionRepository(RepositoryType repositoryType) {
        return aggregatedTransactionRepositories.get(repositoryType);
    }
}
