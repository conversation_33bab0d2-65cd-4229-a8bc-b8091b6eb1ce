package com.aa.ccrecon.accounting.aggregation.config;

import com.aa.ccrecon.domain.constants.SalesSource;
import jakarta.validation.constraints.Min;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.bind.ConstructorBinding;
import org.springframework.boot.context.properties.bind.DefaultValue;
import org.springframework.validation.annotation.Validated;

import java.util.Arrays;
import java.util.List;

@Getter
@Validated
@ConfigurationProperties(prefix = "ccrecon.aggregate")
public class AppConfig {

    private final List<String> salesSource = Arrays.stream(SalesSource.values())
            .map(SalesSource::name).toList();

    @Min(1)
    private final int maxBatchSize;

    @ConstructorBinding
    public AppConfig(@DefaultValue("5") int maxBatchSize) {
        this.maxBatchSize = maxBatchSize;
    }
}
