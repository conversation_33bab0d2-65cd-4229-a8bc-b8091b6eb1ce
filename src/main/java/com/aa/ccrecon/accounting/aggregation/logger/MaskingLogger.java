package com.aa.ccrecon.accounting.aggregation.logger;

import com.aa.itfacs.pmt.mask.Masked;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ClassUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Objects;
import java.util.stream.Collectors;

public class MaskingLogger {

    private static final Logger log = LoggerFactory.getLogger(MaskingLogger.class);

    private final Logger classLogger;

    public MaskingLogger(Class<?> clazz) {
        classLogger = LoggerFactory.getLogger(clazz);
    }

    public void debug(String var1, Object... var2) {
        classLogger.debug(var1, objectsToStrings(var2));
    }

    public void info(String var1, Object... var2) {
        classLogger.info(var1, objectsToStrings(var2));
    }

    public void warn(String var1, Object... var2) {
        classLogger.warn(var1, objectsToStrings(var2));
    }

    public void error(String var1, Throwable var2) {
        classLogger.error(var1, var2);
    }

    public void error(String var1, Object... var2) {
        classLogger.error(var1, objectsToStrings(var2));
    }

    private Object[] objectsToStrings(Object... objs) {
        if (objs != null && objs.length > 0) {
            return objectsToStrings(Arrays.stream(objs).collect(Collectors.toCollection(ArrayList::new)));
        }

        return new Object[0];
    }

    private Object[] objectsToStrings(Collection<?> objs) {
        if (!objs.isEmpty()) {
            return objs.stream()
                    .map(obj -> obj instanceof Collection ? objectsToStrings((Collection<?>)obj) : objectToString(obj))
                    .toArray();
        }

        return new Object[0];
    }

    private Object objectToString(Object obj) {
        try {
            return isNonMaskableObject(obj) ? obj : Masked.objectToMaskedString(obj);
        } catch (Exception ex) {
            log.error("Masking error '{}' for object with class '{}'", ex.getMessage(), obj.getClass().getName());
            return obj.getClass().getName();
        }
    }

    private boolean isNonMaskableObject(Object obj) {
        return Objects.isNull(obj)
                || ClassUtils.isPrimitiveOrWrapper(obj.getClass())
                || obj instanceof String
                || obj instanceof Enum<?>;
    }

}
