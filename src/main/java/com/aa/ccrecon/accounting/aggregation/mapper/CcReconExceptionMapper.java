package com.aa.ccrecon.accounting.aggregation.mapper;

import com.aa.ccrecon.accounting.aggregation.utils.AppConstants;
import com.aa.ccrecon.domain.composite.header.CcReconException;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.time.LocalDateTime;
import java.util.UUID;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, imports = { LocalDateTime.class })
public interface CcReconExceptionMapper {

    @Mapping(target = "exceptionUUID", expression = "java(exceptionUuid)")
    @Mapping(target = "exceptionCode", expression = "java(exceptionCode)")
    @Mapping(target = "createdDate", expression = "java(LocalDateTime.now())")
    @Mapping(target = "exceptionStatus", expression = "java(getExceptionStatus())")
    @Mapping(target = "exceptionSource", expression = "java(getExceptionSource(fileName))")
    CcReconException mapCcReconException(String exceptionUuid, String exceptionCode, String fileName);

    default CcReconException mapCcReconException(String exceptionCode, String fileName) {
        return mapCcReconException(UUID.randomUUID().toString(), exceptionCode, fileName);
    }

    default String getExceptionStatus() {
        return AppConstants.EXCEPTION_STATUS;
    }

    default String getExceptionSource(String fileName) {
        return String.format("%s - %s", AppConstants.SOURCE_ID, fileName);
    }
}
