package com.aa.ccrecon.accounting.aggregation.processors.uatp;

import com.aa.ccrecon.accounting.aggregation.config.AppConfig;
import com.aa.ccrecon.accounting.aggregation.factory.MaskingLoggerFactory;
import com.aa.ccrecon.accounting.aggregation.logger.MaskingLogger;
import com.aa.ccrecon.accounting.aggregation.mapper.StringResultRowMapper;
import com.aa.ccrecon.accounting.aggregation.processors.AbstractExecutor;
import com.aa.ccrecon.accounting.aggregation.processors.AggregateRequest;
import com.aa.ccrecon.accounting.aggregation.utils.YamlPropertySourceFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.Types;
import java.util.List;

@Component
@RequiredArgsConstructor
@PropertySource(value = "classpath:/sql/UATPAggregateQueries.yaml", factory = YamlPropertySourceFactory.class)
public class UATPAssignGlobalIdCommand extends AbstractExecutor
{
    @Value("${UATPAggregate_assignGlobalId_1}")
    private String assignGlobalId1;
    @Value("${UATPAggregate_assignGlobalId_2}")
    private String assignGlobalId2;
    @Value("${UATPAggregate_assignGlobalId_3}")
    private String assignGlobalId3;
    @Value("${UATPAggregate_assignGlobalId_4}")
    private String assignGlobalId4;
    @Value("${UATPAggregate_assignGlobalId_5}")
    private String assignGlobalId5;
//    @Value("${UATPAggregate_assignGlobalId_6}")
//    private String assignGlobalId6;
//    @Value("${UATPAggregate_assignGlobalId_7}")
//    private String assignGlobalId7;
    @Value("${UATPAggregate_getGlobalIds}")
    private String getGlobalIds;

    private final JdbcTemplate jdbcTemplate;

    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    private final AppConfig appConfig;

    private static final MaskingLogger log = MaskingLoggerFactory.getLogger(UATPAssignGlobalIdCommand.class);

    @Override
    public void execute (AggregateRequest request) {

        try {
            log.debug("Starting assigning UATP global IDs..");

            MapSqlParameterSource params = new MapSqlParameterSource()
                    .addValue("maxBatchSize", appConfig.getMaxBatchSize(), Types.DOUBLE)
                    .addValue("sales_source", appConfig.getSalesSource());

            namedParameterJdbcTemplate.update(assignGlobalId1, params);
            namedParameterJdbcTemplate.update(assignGlobalId2, params);
            jdbcTemplate.execute(assignGlobalId3);
            jdbcTemplate.execute(assignGlobalId4);
            jdbcTemplate.execute(assignGlobalId5);
//            jdbcTemplate.execute(assignGlobalId6);
//            jdbcTemplate.execute(assignGlobalId7);
            List<String> globalIds = jdbcTemplate.query(getGlobalIds, new StringResultRowMapper());

            log.debug("UATP Global ID's assigned: {}", globalIds);
        }
        catch (DataAccessException dae) {
            log.error("Failed to assign Global ID's (Step 4-8)", dae);
            throw dae;
        }

        super.execute(request);
    }

}
