package com.aa.ccrecon.accounting.aggregation.repository;

import com.aa.ccrecon.accounting.aggregation.mapper.AggregateTransactionProjection;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;

import java.util.List;

@NoRepositoryBean
public interface AggregateReceivableAccountingTransRepository {

    List<AggregateTransactionProjection> findUnprocessedAggregatedTransactions();

    Integer updateAggregatedTransactionsWithGlobalIdAndIsProcessedFalse(@Param("globalId") String globalId);

}
