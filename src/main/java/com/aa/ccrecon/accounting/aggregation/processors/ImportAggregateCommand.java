package com.aa.ccrecon.accounting.aggregation.processors;

import com.aa.ccrecon.accounting.aggregation.config.AppConfig;
import com.aa.ccrecon.accounting.aggregation.factory.MaskingLoggerFactory;
import com.aa.ccrecon.accounting.aggregation.logger.MaskingLogger;
import com.aa.ccrecon.accounting.aggregation.utils.YamlPropertySourceFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import static com.aa.ccrecon.accounting.aggregation.processors.AggregateRequest.RequestValues;

@Component
@RequiredArgsConstructor
@PropertySource(value = "classpath:sql/NON_UATPAggregateQuery.yaml", factory = YamlPropertySourceFactory.class)
public class ImportAggregateCommand extends AbstractExecutor {

    private static final MaskingLogger log = MaskingLoggerFactory.getLogger(ImportAggregateCommand.class);

    @Value("${ImportAggregate}")
    private String sql;

    final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    final AppConfig appConfig;

    @Override
    public void execute (AggregateRequest request)
    {
        log.debug("Starting aggregation process..");
        MapSqlParameterSource params = new MapSqlParameterSource()
                .addValue("sales_source", appConfig.getSalesSource());

        Integer retval = namedParameterJdbcTemplate.queryForObject(sql, params, Integer.class);
        request.addValue(RequestValues.AGGREGATE_COUNT, retval);
        log.debug("Aggregate complete with {} aggregated rows", retval);

        super.execute(request);
    }



}
