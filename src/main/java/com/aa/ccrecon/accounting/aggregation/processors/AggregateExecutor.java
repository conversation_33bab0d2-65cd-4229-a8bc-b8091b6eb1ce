package com.aa.ccrecon.accounting.aggregation.processors;

import com.aa.ccrecon.accounting.aggregation.factory.AggregatedTransactionRepositoryFactory.RepositoryType;
import com.aa.ccrecon.accounting.aggregation.factory.MaskingLoggerFactory;
import com.aa.ccrecon.accounting.aggregation.logger.MaskingLogger;
import com.aa.ccrecon.domain.composite.header.CcReconHeader;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
public class AggregateExecutor extends AbstractExecutor {

    private static final MaskingLogger log = MaskingLoggerFactory.getLogger(AggregateExecutor.class);

    private final AggregateRequest request = new AggregateRequest();

    @Transactional
    public void execute(CcReconHeader ccReconHeader, String sourceId, RepositoryType repositoryType) {
        try {
            request.addValue(AggregateRequest.RequestValues.CC_RECON_HEADER, ccReconHeader);
            request.addValue(AggregateRequest.RequestValues.SOURCE_ID, sourceId);
            request.addValue(AggregateRequest.RequestValues.REPOSITORY_TYPE, repositoryType);
            super.execute(request);
        }
        catch (DataAccessException dae) {
            log.error("SQL error", dae);
        }
        catch (Exception e) {
            log.error("Processor execution error", e);
        }
    }

}
