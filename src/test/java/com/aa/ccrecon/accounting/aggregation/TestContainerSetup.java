package com.aa.ccrecon.accounting.aggregation;

import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.utility.DockerImageName;
import org.testcontainers.utility.MountableFile;

public abstract class TestContainerSetup {

    static final String INCOMING_Q = "consumeServiceBusDetailPaymentMessage";
    static final String OUTGOING_Q = "supplyServiceBusAggregatedTransactionMessage";
    static final String EXCEPTION_Q = "supplyServiceBusException";

    static DockerImageName POSTGRES_TEST_IMAGE = DockerImageName.parse("postgres:15.4");

    static PostgreSQLContainer<?> ipsPostgres = new PostgreSQLContainer<>(POSTGRES_TEST_IMAGE)
            .withDatabaseName("findatahub_curated")
            .withCopyFileToContainer(
                    MountableFile.forClasspathResource("/sql/schema.sql"),
                    "/docker-entrypoint-initdb.d/1schema.sql"
            )
            .withPassword("test").withUsername("test");

    static {
        ipsPostgres.start();
    }

    @DynamicPropertySource
    static void setProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.cloud.azure.compatibility-verifier.enabled:", () -> false);
        registry.add("spring.datasource.url", ipsPostgres::getJdbcUrl);
        registry.add("spring.datasource.username", ipsPostgres::getUsername);
        registry.add("spring.datasource.password", ipsPostgres::getPassword);

        registry.add("retry.times",() -> "2");
        registry.add("retry.delay",() -> "1");
    }
}
