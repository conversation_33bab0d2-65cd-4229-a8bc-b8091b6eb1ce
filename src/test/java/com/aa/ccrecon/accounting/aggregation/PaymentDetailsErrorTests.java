package com.aa.ccrecon.accounting.aggregation;

import com.aa.ccrecon.accounting.aggregation.config.TestServiceBusConfig;
import com.aa.ccrecon.accounting.aggregation.entity.AggregateGlReceivableAccountingTransEntity;
import com.aa.ccrecon.accounting.aggregation.entity.AggregateGlUatpAggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.accounting.aggregation.mapper.IntegerResultRowMapper;
import com.aa.ccrecon.accounting.aggregation.repository.AggregateGlReceivableAccountingTransRepository;
import com.aa.ccrecon.accounting.aggregation.repository.AggregateGlUatpReceivableAccountingTransRepository;
import com.aa.ccrecon.accounting.aggregation.utils.AppConstants;
import com.aa.ccrecon.accounting.aggregation.utils.ModelExtractor;
import com.aa.ccrecon.accounting.aggregation.utils.TestModelExtractor;
import com.aa.ccrecon.accounting.aggregation.utils.YamlPropertySourceFactory;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.composite.CompositeModel;
import com.aa.ccrecon.domain.composite.header.CcReconException;
import com.aa.itfacs.pmt.mask.Masked;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.itfacs.pmt.controller.StreamInputRequest;
import com.itfacs.pmt.controller.StreamOutputsResponse;
import net.minidev.json.JSONObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.cloud.stream.binder.test.EnableTestBinder;
import org.springframework.context.annotation.PropertySource;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static org.hamcrest.CoreMatchers.containsString;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.when;

@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles({"test"})
@ExtendWith({OutputCaptureExtension.class})
@EnableTestBinder
@ContextConfiguration(classes = TestServiceBusConfig.class)
@PropertySource(value= "classpath:sql/NON_UATPAggregateQuery.yaml", factory = YamlPropertySourceFactory.class)
@PropertySource(value="classpath:sql/UATPAggregateQueries.yaml", factory = YamlPropertySourceFactory.class)
@PropertySource(value="classpath:sql/TestQueries.yaml", factory = YamlPropertySourceFactory.class)
public class PaymentDetailsErrorTests extends TestContainerSetup {
    @Autowired
    private TestComponent testComponent;

    @SpyBean
    private AggregateGlReceivableAccountingTransRepository aggregateGlReceivableAccountingTransRepository;

    @Autowired
    private AggregateGlUatpReceivableAccountingTransRepository aggregateGlUatpReceivableAccountingTransRepository;

    @SpyBean
    private JdbcTemplate jdbcTemplate;

    @SpyBean
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Value("${VerifyAvailableAggregate}")
    private String verifyAvailableAggregate;

    @Value("${ImportAggregate}")
    private String importAggregate;

    @Value("${AssignGlobalId}")
    private String assignGlobalId;

    @Value("${VerifyTransDetails}")
    private String verifyTransDetails;

    @Value("${UATPAggregate_aggregateData3}")
    private String UATPAggregate_aggregateData3;

    @Value("${UATPAggregate_assignGlobalId_4}")
    private String UATPAggregate_assignGlobalId4;

    private static ObjectMapper objectMapper;

    private static DataAccessException mockException;

    @BeforeAll
    public static void init(){
        objectMapper = JsonMapper.builder()
                .addModule(new JavaTimeModule())
                .build();
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        mockException = Mockito.mock(DataAccessException.class);
        when(mockException.getMessage())
                .thenReturn("Mock exception");
    }

    /***
     * END of file notice
     * @param output - Application output
     * @throws IOException - Processing exception
     */
    @Test
    @Sql(value = {"/sql/clean.sql", "/sql/aggregategl_uatp.sql"}, executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenEventStateEnd_WhenMessageReceivedAndUatpAggregateErrorThrown_ThenVerifyMessageLoggedAndRolledBack(CapturedOutput output) throws IOException {
        doThrow(mockException).when(jdbcTemplate)
                .execute(UATPAggregate_aggregateData3);

        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            JSONObject jsonInputFile = objectMapper.readValue(isr, JSONObject.class);

            StreamOutputsResponse messages = testComponent.processEvent(new StreamInputRequest(1, 1, INCOMING_Q, List.of(EXCEPTION_Q, OUTGOING_Q), jsonInputFile));

            CompositeModel araFileMessage = objectMapper.readValue(jsonInputFile.toString(), CompositeModel.class);
            String sourceId = araFileMessage.getMessageEnvelope().getMessageHeader().getSourceContext().getSourceId();
            Assertions.assertTrue(output.getOut().contains("New message received: '" + Masked.objectToMaskedString(araFileMessage) + "'"));
            Assertions.assertTrue(output.getOut().contains("End of File message received. File: " + sourceId));
            Assertions.assertTrue(output.getOut().contains("Assignment of Aggregate ID's failed (Step 1-3)"));

            verifyRollback(output, messages);
        }
    }

    /***
     * END of file notice
     * @param output - Application output
     * @throws IOException - Processing exception
     */
    @Test
    @Sql(value = {"/sql/clean.sql", "/sql/aggregategl_uatp.sql"}, executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenEventStateEnd_WhenMessageReceivedAndUatpAssignErrorThrown_ThenVerifyMessageLoggedAndRolledBack(CapturedOutput output) throws IOException {
        doThrow(mockException).when(jdbcTemplate)
                .execute(UATPAggregate_assignGlobalId4);

        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            JSONObject jsonInputFile = objectMapper.readValue(isr, JSONObject.class);

            StreamOutputsResponse messages = testComponent.processEvent(new StreamInputRequest(1, 1, INCOMING_Q, List.of(EXCEPTION_Q, OUTGOING_Q), jsonInputFile));

            CompositeModel araFileMessage = objectMapper.readValue(jsonInputFile.toString(), CompositeModel.class);
            String sourceId = araFileMessage.getMessageEnvelope().getMessageHeader().getSourceContext().getSourceId();
            Assertions.assertTrue(output.getOut().contains("New message received: '" + Masked.objectToMaskedString(araFileMessage) + "'"));
            Assertions.assertTrue(output.getOut().contains("End of File message received. File: " + sourceId));
            Assertions.assertTrue(output.getOut().contains("Failed to assign Global ID's (Step 4-8)"));

            verifyRollback(output, messages);
        }
    }

    /***
     * Test bad data
     * @param output - Application output
     */
    @Test
    void givenBadData_WhenMessageReceivedAndProcessed_ThenVerifyAggregatedTransactionNotSent(CapturedOutput output) throws IOException {
        try(InputStream isr = new ByteArrayInputStream(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/bad_data.txt")).readAllBytes())) {
            String str = new String(isr.readAllBytes(), StandardCharsets.UTF_8);

            StreamOutputsResponse messages = testComponent.processEvent(new StreamInputRequest(1, 1, INCOMING_Q, List.of(EXCEPTION_Q, OUTGOING_Q), str));

            Assertions.assertTrue(output.getOut().contains("Error processing message"));
            Assertions.assertTrue(output.getOut().contains(ClassCastException.class.getCanonicalName()));


            byte[] message = (byte[]) messages.getOutputs().get(OUTGOING_Q + "0");

            Assertions.assertFalse(output.getOut().contains("Going to add message to Sinks.Many."));
            Assertions.assertNull(message);
        }
    }

    /***
     * END of file notice
     * @param output - Application output
     * @throws IOException - Processing exception
     */
    @Test
    @Sql(value = {"/sql/clean.sql", "/sql/aggregategl.sql"}, executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenEventStateEnd_WhenMessageReceivedAndVerifyErrorThrown_ThenVerifyAggregatedTransactionNotSent(CapturedOutput output) throws IOException {
        doThrow(mockException).when(jdbcTemplate)
                .queryForObject(verifyAvailableAggregate, Integer.class);

        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            JSONObject jsonInputFile = objectMapper.readValue(isr, JSONObject.class);

            StreamOutputsResponse messages = testComponent.processEvent(new StreamInputRequest(1, 1, INCOMING_Q, List.of(EXCEPTION_Q, OUTGOING_Q), jsonInputFile));

            CompositeModel araFileMessage = objectMapper.readValue(jsonInputFile.toString(), CompositeModel.class);
            String sourceId = araFileMessage.getMessageEnvelope().getMessageHeader().getSourceContext().getSourceId();
            Assertions.assertTrue(output.getOut().contains("New message received: '" + Masked.objectToMaskedString(araFileMessage) + "'"));
            Assertions.assertTrue(output.getOut().contains("End of File message received. File: " + sourceId));

            verifyRollback(output, messages);
        }
    }

    /***
     * END of file notice
     * @param output - Application output
     * @throws IOException - Processing exception
     */
    @Test
    @Sql(value = {"/sql/clean.sql", "/sql/aggregategl.sql"}, executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenEventStateEnd_WhenMessageReceivedAndImportErrorThrown_ThenVerifyAggregatedTransactionNotSent(CapturedOutput output) throws IOException {
        doThrow(mockException).when(namedParameterJdbcTemplate).
                queryForObject(eq(importAggregate), any(MapSqlParameterSource.class), eq(Integer.class));

        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            JSONObject jsonInputFile = objectMapper.readValue(isr, JSONObject.class);

            StreamOutputsResponse messages = testComponent.processEvent(new StreamInputRequest(1, 1, INCOMING_Q, List.of(EXCEPTION_Q, OUTGOING_Q), jsonInputFile));

            CompositeModel araFileMessage = objectMapper.readValue(jsonInputFile.toString(), CompositeModel.class);
            String sourceId = araFileMessage.getMessageEnvelope().getMessageHeader().getSourceContext().getSourceId();
            Assertions.assertTrue(output.getOut().contains("New message received: '" + Masked.objectToMaskedString(araFileMessage) + "'"));
            Assertions.assertTrue(output.getOut().contains("End of File message received. File: " + sourceId));

            verifyRollback(output, messages);
        }
    }

    /***
     * END of file notice
     * @param output - Application output
     * @throws IOException - Processing exception
     */
    @Test
    @Sql(value = {"/sql/clean.sql", "/sql/aggregategl.sql"}, executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenEventStateEnd_WhenMessageReceivedAndAssignErrorThrown_ThenVerifyAggregatedTransactionNotSent(CapturedOutput output) throws IOException {
        doThrow(mockException).when(namedParameterJdbcTemplate)
                .query(Mockito.eq(assignGlobalId), any(MapSqlParameterSource.class), any(IntegerResultRowMapper.class));

        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            JSONObject jsonInputFile = objectMapper.readValue(isr, JSONObject.class);

            StreamOutputsResponse messages = testComponent.processEvent(new StreamInputRequest(1, 1, INCOMING_Q, List.of(EXCEPTION_Q, OUTGOING_Q), jsonInputFile));

            CompositeModel araFileMessage = objectMapper.readValue(jsonInputFile.toString(), CompositeModel.class);
            String sourceId = araFileMessage.getMessageEnvelope().getMessageHeader().getSourceContext().getSourceId();
            Assertions.assertTrue(output.getOut().contains("New message received: '" + Masked.objectToMaskedString(araFileMessage) + "'"));
            Assertions.assertTrue(output.getOut().contains("End of File message received. File: " + sourceId));

            verifyRollback(output, messages);
        }
    }

    /***
     * END of file notice
     * @param output - Application output
     * @throws IOException - Processing exception
     */
    @Test
    @Sql(value = {"/sql/clean.sql", "/sql/aggregategl.sql"}, executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenEventStateEnd_WhenMessageReceivedAndAggregateGlErrorThrown_ThenVerifyAggregatedTransactionNotSent(CapturedOutput output) throws IOException {
        doThrow(mockException).when(aggregateGlReceivableAccountingTransRepository)
                .findUnprocessedAggregatedTransactions();

        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            JSONObject jsonInputFile = objectMapper.readValue(isr, JSONObject.class);

            StreamOutputsResponse messages = testComponent.processEvent(new StreamInputRequest(1, 1, INCOMING_Q, List.of(EXCEPTION_Q, OUTGOING_Q), jsonInputFile));

            CompositeModel araFileMessage = objectMapper.readValue(jsonInputFile.toString(), CompositeModel.class);
            String sourceId = ModelExtractor.getSourceId.apply(araFileMessage).orElse(null);
            Assertions.assertTrue(output.getOut().contains("New message received: '" + Masked.objectToMaskedString(araFileMessage) + "'"));
            Assertions.assertTrue(output.getOut().contains("End of File message received. File: " + sourceId));
            Assertions.assertTrue(output.getOut().contains("Mock exception"));
            assertThat(output.getAll(), containsString("Aggregate exception: AGGREGATION_ACCOUNTING_FAILED"));

            byte[] exceptionMessage = (byte[]) messages.getOutputs().get(EXCEPTION_Q + "0");
            if (exceptionMessage != null) {
                AggregatedTransaction aggregatedTransaction = objectMapper.readValue(exceptionMessage, AggregatedTransaction.class);
                List<CcReconException> ccReconExceptions = TestModelExtractor.getCcReconExceptions.apply(aggregatedTransaction).orElse(new ArrayList<>());

                Assertions.assertTrue(ccReconExceptions.stream().anyMatch(ccReconException ->
                        ccReconException.getExceptionCode().equals(AppConstants.AGGREGATION_ACCOUNTING_FAILED) &&
                                ccReconException.getExceptionStatus().equals(AppConstants.EXCEPTION_STATUS) &&
                                ccReconException.getExceptionSource().equals(String.format("%s - %s", AppConstants.SOURCE_ID, sourceId))));

                verifyRollback(output, messages);
            }
            else {
                Assertions.fail("Exception message not found");
            }
        }
    }

    private void verifyRollback(CapturedOutput output, StreamOutputsResponse outMsgs) {
        Assertions.assertTrue(output.getOut().contains("Mock exception"));
        Assertions.assertTrue(output.getOut().contains("Transaction rollback"));

        byte[] aggregatedTransactionMessage = (byte[]) outMsgs.getOutputs().get(OUTGOING_Q + "0");

        Assertions.assertNull(aggregatedTransactionMessage);

        Integer numRows = jdbcTemplate.queryForObject(verifyTransDetails, Integer.class);
        List<AggregateGlReceivableAccountingTransEntity> glTransactions = aggregateGlReceivableAccountingTransRepository.findAll();
        List<AggregateGlUatpAggregateReceivableAccountingTransEntity> uatpTransactions = aggregateGlUatpReceivableAccountingTransRepository.findAll();

        Assertions.assertEquals(0, numRows);
        Assertions.assertTrue(glTransactions.isEmpty());
        Assertions.assertTrue(uatpTransactions.isEmpty());
    }

}
