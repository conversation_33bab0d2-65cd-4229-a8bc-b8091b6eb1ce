package com.aa.ccrecon.accounting.aggregation.config;

import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.messaging.Message;
import reactor.core.publisher.Sinks;

@TestConfiguration
public class TestServiceBusConfig {
    @Bean("aggregatedTransactionSink")
    @Primary
    public Sinks.Many<Message<AggregatedTransaction>> serviceBusAggregatedTransactionSink() {
        return Sinks.many().multicast().onBackpressureBuffer();
    }

    @Bean("exceptionSink")
    @Primary
    public Sinks.Many<Message<AggregatedTransaction>> serviceBusExceptionSink() {
        return Sinks.many().multicast().onBackpressureBuffer();
    }
}