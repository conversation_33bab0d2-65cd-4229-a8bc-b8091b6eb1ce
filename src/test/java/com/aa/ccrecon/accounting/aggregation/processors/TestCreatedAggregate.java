package com.aa.ccrecon.accounting.aggregation.processors;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;

import com.aa.ccrecon.accounting.aggregation.TestContainerSetup;

import static org.junit.jupiter.api.Assertions.assertEquals;


@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@SpringBootTest
public class TestCreatedAggregate extends TestContainerSetup {

    @Autowired
    ImportAggregateCommand importAggregateCommand;

    @Test
    @Sql(value = "/sql/aggregategl.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenTransactions_whenAllAreValid_thenVerifyCount()
    {
        AggregateRequest req = new AggregateRequest();
        importAggregateCommand.execute(req);

        Integer ag = req.getValue(AggregateRequest.RequestValues.AGGREGATE_COUNT, Integer.class);
        assert (ag.equals(2));
    }

    @Test
    @Sql(value = "/sql/clean.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenTransactions_whenNoTransactions_thenCount0()
    {
        AggregateRequest req = new AggregateRequest();
        importAggregateCommand.execute(req);

        Integer ag = req.getValue(AggregateRequest.RequestValues.AGGREGATE_COUNT, Integer.class);
        assertEquals(0, ag);
    }

}
