package com.aa.ccrecon.accounting.aggregation.service;

import com.aa.ccrecon.accounting.aggregation.factory.AggregatedTransactionRepositoryFactory;
import com.aa.ccrecon.accounting.aggregation.factory.AggregatedTransactionRepositoryFactory.RepositoryType;
import com.aa.ccrecon.accounting.aggregation.mapper.AggregateTransactionProjection;
import com.aa.ccrecon.accounting.aggregation.processors.AggregateTransactionProjectionTestImpl;
import com.aa.ccrecon.accounting.aggregation.repository.AggregateGlReceivableAccountingTransRepository;
import com.aa.ccrecon.accounting.aggregation.repository.AggregateGlUatpReceivableAccountingTransRepository;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.constants.SalesSource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

import static com.aa.ccrecon.domain.subledger.TransactionType.TYPES.SALE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AggregateTransactionServiceTests {

	@Mock
	private RetryService mockRetryService;

	@Mock
	private AggregatedTransactionRepositoryFactory aggregatedTransactionRepositoryFactory;

	@Mock
	private AggregateGlReceivableAccountingTransRepository nonUatpAggregatedTransactionRepository;

	@Mock
	private AggregateGlUatpReceivableAccountingTransRepository uatpAggregatedTransactionRepository;

	@InjectMocks
	private AggregateTransactionService aggregateTransactionService;

	@Test
	@DisplayName(value = "given findUnprocessedAggregatedTransactions method is called, when there is unprocessed data is in database, return List<AggregateTransactionProjection>")
	public void givenAggregateTransactions_WhenUnprocessedTransactionsFound_ThenVerifyFoundTransactionCount() {
		List<AggregateTransactionProjection> expectedProjectionList = getAggregateTransactionProjectionTestList(2, 10, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY);

		Mockito.when(aggregatedTransactionRepositoryFactory.getAggregatedTransactionRepository(RepositoryType.AGGREGATE)).thenReturn(nonUatpAggregatedTransactionRepository);
		when(mockRetryService.retry(any(Supplier.class))).thenReturn(expectedProjectionList);

		List<AggregateTransactionProjection> actualProjectionList = aggregateTransactionService.findUnprocessedAggregatedTransactions(RepositoryType.AGGREGATE);
		
		Mockito.verify(mockRetryService, times(1)).retry(any(Supplier.class));
		Assertions.assertEquals(expectedProjectionList.size(), actualProjectionList.size());
	}

	@Test
	@DisplayName(value = "given findUnprocessedAggregatedTransactions method is called, when there is unprocessed UATP data is in database, return List<AggregateTransactionProjection>")
	public void givenUatpAggregateTransactions_WhenUnprocessedTransactionsFound_ThenVerifyFoundTransactionCount() {
		List<AggregateTransactionProjection> expectedProjectionList = getAggregateTransactionProjectionTestList(2, 10, MessageType.AGGREGATED_GL_UATP, SalesSource.COMPANY);

		Mockito.when(aggregatedTransactionRepositoryFactory.getAggregatedTransactionRepository(RepositoryType.UATP_AGGREGATE)).thenReturn(uatpAggregatedTransactionRepository);
		when(mockRetryService.retry(any(Supplier.class))).thenReturn(expectedProjectionList);

		List<AggregateTransactionProjection> actualProjectionList = aggregateTransactionService.findUnprocessedAggregatedTransactions(RepositoryType.UATP_AGGREGATE);

		Mockito.verify(mockRetryService, times(1)).retry(any(Supplier.class));
		Assertions.assertEquals(expectedProjectionList.size(), actualProjectionList.size());
	}
	
	@Test
	@DisplayName(value = "given updateAggregatedTransactionsWithGlobalIdAndIsProcessedFalse method is called, when there is unprocessed data is in database, return numberOfRows updated")
	public void givenAggregateTransactions_WhenAggregatedTransactionsUpdated_ThenVerifyUpdatedTransactionCount() {
		String globalId = "5432";
		Integer expectedRowsUpdated = Integer.valueOf(2);
		
		when(mockRetryService.retry(any(Supplier.class))).thenReturn(expectedRowsUpdated);

		Integer actualRowsUpdated = aggregateTransactionService.updateAggregatedTransactionsWithGlobalIdAndIsProcessedFalse(RepositoryType.AGGREGATE, globalId);
		
		Mockito.verify(mockRetryService, times(1)).retry(any(Supplier.class));
		
		Assertions.assertEquals(expectedRowsUpdated, actualRowsUpdated);
	}

	private List<AggregateTransactionProjection> getAggregateTransactionProjectionTestList(int numberOfProjectons,
																						   int countForEach, MessageType messageType, SalesSource salesSource) {

		List<AggregateTransactionProjection> projectionList = new ArrayList<>();

		for (int i = 1; i <= numberOfProjectons; i++) {
			AggregateTransactionProjection projection = new AggregateTransactionProjectionTestImpl(String.valueOf(i),
					(long) countForEach, messageType, salesSource, SALE);
			projectionList.add(projection);
		}

		return projectionList;

	}

}
