package com.aa.ccrecon.accounting.aggregation.processors;

import com.aa.ccrecon.accounting.aggregation.mapper.StringResultRowMapper;
import com.aa.ccrecon.accounting.aggregation.processors.uatp.UATPAssignGlobalIdCommand;
import com.aa.ccrecon.accounting.aggregation.repository.AggregateGlReceivableAccountingTransRepository;
import com.aa.ccrecon.accounting.aggregation.repository.AggregateGlUatpReceivableAccountingTransRepository;
import com.aa.ccrecon.accounting.aggregation.utils.YamlPropertySourceFactory;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.context.annotation.PropertySource;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.transaction.PlatformTransactionManager;
import org.testcontainers.shaded.com.fasterxml.jackson.core.JsonProcessingException;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@SpringBootTest
@EnableAutoConfiguration(exclude = {DataSourceAutoConfiguration.class})
@PropertySource(value="classpath:sql/UATPAggregateQueries.yaml", factory = YamlPropertySourceFactory.class)
@ExtendWith({OutputCaptureExtension.class})
public class UATPAssignGlobalIdCommandTests {

    @MockBean
    JdbcTemplate jdbcTemplate;

    @MockBean
    NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @MockBean
    AggregateGlReceivableAccountingTransRepository aggregateGlReceivableAccountingTransRepository;

    @MockBean
    AggregateGlUatpReceivableAccountingTransRepository aggregateGlUatpReceivableAccountingTransRepository;

    @MockBean
    PlatformTransactionManager platformTransactionManager;

    @Value("${UATPAggregate_assignGlobalId_1}")
    private String assignGlobalId1;

    @Value("${UATPAggregate_assignGlobalId_2}")
    private String assignGlobalId2;

    @Value("${UATPAggregate_assignGlobalId_3}")
    private String assignGlobalId3;

    @Value("${UATPAggregate_assignGlobalId_4}")
    private String assignGlobalId4;

//    @Value("${UATPAggregate_assignGlobalId_5}")
//    private String assignGlobalId5;

    @Value("${UATPAggregate_getGlobalIds}")
    private String getGlobalIds;

    @SpyBean
    UATPAssignGlobalIdCommand uatpAssignGlobalIdCommand;

    @Test
    public void givenAssignUatpGlobalIdExecution_WhenAssigningGlobalIds_VerifyDatabaseCalls(CapturedOutput output) throws JsonProcessingException {

        List<String> globalIds = Collections.singletonList("UATP-1");
        doReturn(globalIds).when(jdbcTemplate)
                .query(eq(getGlobalIds), Mockito.any(StringResultRowMapper.class));

        uatpAssignGlobalIdCommand.execute(new AggregateRequest());

        Mockito.verify(namedParameterJdbcTemplate, times(1))
                .update(eq(assignGlobalId1), any(MapSqlParameterSource.class));
        Mockito.verify(namedParameterJdbcTemplate, times(1))
                .update(eq(assignGlobalId2), any(MapSqlParameterSource.class));
        Mockito.verify(jdbcTemplate, Mockito.times(1))
                .execute(assignGlobalId3);
        Mockito.verify(jdbcTemplate, Mockito.times(1))
                .execute(assignGlobalId4);
//        Mockito.verify(jdbcTemplate, Mockito.times(1))
//                .execute(assignGlobalId5);
        Mockito.verify(jdbcTemplate, Mockito.times(1))
                .query(eq(getGlobalIds), Mockito.any(StringResultRowMapper.class));
        assertTrue(output.getOut().contains("UATP Global ID's assigned: [UATP-1]"));
    }

    @Test
    public void givenAssignUatpGlobalIdExecution_WhenErrorAssigningGlobalIds_VerifyDatabaseCalls(CapturedOutput output) {

        DataAccessException mockException = Mockito.mock(DataAccessException.class);
        when(mockException.getMessage())
                .thenReturn("Mock exception");

        doThrow(mockException).when(jdbcTemplate)
                .execute(assignGlobalId3);

        try {
            uatpAssignGlobalIdCommand.execute(new AggregateRequest());
        }
        catch (Exception e) {
            Mockito.verify(namedParameterJdbcTemplate, times(1))
                    .update(eq(assignGlobalId1), any(MapSqlParameterSource.class));
            Mockito.verify(namedParameterJdbcTemplate, times(1))
                    .update(eq(assignGlobalId2), any(MapSqlParameterSource.class));
            Mockito.verify(jdbcTemplate, Mockito.times(1))
                    .execute(assignGlobalId3);
            Mockito.verify(jdbcTemplate, Mockito.times(0))
                    .execute(assignGlobalId4);
//            Mockito.verify(jdbcTemplate, Mockito.times(0))
//                    .execute(assignGlobalId5);
            Mockito.verify(jdbcTemplate, Mockito.times(0))
                    .query(eq(getGlobalIds), Mockito.any(StringResultRowMapper.class));
            assertTrue(output.getOut().contains("Failed to assign Global ID's (Step 4-8)"));
        }
    }
}
