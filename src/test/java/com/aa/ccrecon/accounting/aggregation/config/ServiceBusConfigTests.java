package com.aa.ccrecon.accounting.aggregation.config;

import com.aa.ccrecon.accounting.aggregation.service.AggregateService;
import com.aa.ccrecon.accounting.aggregation.utils.ModelExtractor;
import com.aa.ccrecon.domain.composite.CompositeModel;
import com.aa.ccrecon.domain.composite.header.CcReconHeader;
import com.azure.spring.messaging.checkpoint.Checkpointer;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Objects;

import static com.azure.spring.messaging.AzureHeaders.CHECKPOINTER;
import static org.mockito.Mockito.*;


@ExtendWith(SpringExtension.class)
public class ServiceBusConfigTests {

    @InjectMocks
    ServiceBusConfig serviceBusConfig;

    @Mock
    AggregateService aggregateService;

    @Mock
    Checkpointer checkpointer;

    ObjectMapper objectMapper;

    @BeforeEach
    public void init(){
        objectMapper = new ObjectMapper();
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        doNothing().when(aggregateService).process(any(), any());
        doReturn(Mono.empty()).when(checkpointer).success();
    }

    /***
     * Test End of File message
     */
    @Test
    void givenMessageReceived_WhenEndOfFileEventStateFound_ThenVerifyProcessedAndCheckpointed() throws IOException {
        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            CompositeModel jsonInputFile = objectMapper.readValue(isr, CompositeModel.class);
            CcReconHeader ccReconHeader = ModelExtractor.getCcReconHeader.apply(jsonInputFile).orElse(null);
            String sourceId = ModelExtractor.getSourceId.apply(jsonInputFile).orElse(null);

            Message<CompositeModel> message = MessageBuilder.withPayload(jsonInputFile).setHeader(CHECKPOINTER, checkpointer).build();
            serviceBusConfig.consumeServiceBusDetailPaymentMessage(aggregateService).accept(message);

            Mockito.verify(aggregateService, Mockito.times(1)).process(ccReconHeader, sourceId);
            Mockito.verify(checkpointer, Mockito.times(1)).success();
            Mockito.verify(checkpointer, Mockito.times(0)).failure();
        }
    }

    /***
     * Non END event states
     * @param filename - File name
     */
    @ParameterizedTest
    @CsvSource(
            {
                    "02 SampleContractARA-Trans.json",
                    "01 SampleContractARA-Begin.json"
            }
    )
    void givenMessageReceived_WhenEndOfFileEventStateNotFound_ThenVerifyNotProcessedAndCheckpointed(String filename) throws IOException {
        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/" + filename)))) {
            CompositeModel jsonInputFile = objectMapper.readValue(isr, CompositeModel.class);
            CcReconHeader ccReconHeader = ModelExtractor.getCcReconHeader.apply(jsonInputFile).orElse(null);
            String sourceId = ModelExtractor.getSourceId.apply(jsonInputFile).orElse(null);

            Message<CompositeModel> message = MessageBuilder.withPayload(jsonInputFile).setHeader(CHECKPOINTER, checkpointer).build();
            serviceBusConfig.consumeServiceBusDetailPaymentMessage(aggregateService).accept(message);

            Mockito.verify(aggregateService, Mockito.times(0)).process(ccReconHeader, sourceId);
            Mockito.verify(checkpointer, Mockito.times(1)).success();
            Mockito.verify(checkpointer, Mockito.times(0)).failure();
        }
    }

    /***
     * Runtime error during processing
     */
    @Test
    void givenMessageReceived_WhenErrorProcessingMessage_ThenVerifyProcessedAndCheckpointed() throws IOException {
        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            CompositeModel jsonInputFile = objectMapper.readValue(isr, CompositeModel.class);
            CcReconHeader ccReconHeader = ModelExtractor.getCcReconHeader.apply(jsonInputFile).orElse(null);
            String sourceId = ModelExtractor.getSourceId.apply(jsonInputFile).orElse(null);

            Message<CompositeModel> message = MessageBuilder.withPayload(jsonInputFile).setHeader(CHECKPOINTER, checkpointer).build();
            doThrow(new RuntimeException()).when(aggregateService).process(any(), any());
            serviceBusConfig.consumeServiceBusDetailPaymentMessage(aggregateService).accept(message);

            Mockito.verify(aggregateService, Mockito.times(1)).process(ccReconHeader, sourceId);
            Mockito.verify(checkpointer, Mockito.times(1)).success();
            Mockito.verify(checkpointer, Mockito.times(0)).failure();
        }
    }

    /***
     * Test Enhanced Aggregate Accounting Message with RECEIVABLE_ACCOUNTING
     */
    @Test
    void givenEnhancedAggregateAccountingMessage_WhenReceivableAccountingType_ThenVerifyProcessedAndCheckpointed() throws IOException {
        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/enhanced/EnhancedAggregateAccountingReceivable.json")))) {
            CompositeModel jsonInputFile = objectMapper.readValue(isr, CompositeModel.class);

            Message<CompositeModel> message = MessageBuilder.withPayload(jsonInputFile).setHeader(CHECKPOINTER, checkpointer).build();
            serviceBusConfig.consumeServiceBusDetailPaymentMessage(aggregateService).accept(message);

            Mockito.verify(aggregateService, Mockito.times(1)).process(any(CcReconHeader.class), eq("itfacs-pmt-amex-settlement-********-182429-uat.txt"));
            Mockito.verify(checkpointer, Mockito.times(1)).success();
            Mockito.verify(checkpointer, Mockito.times(0)).failure();
        }
    }

    /***
     * Test Enhanced Aggregate Accounting Message with SETTLEMENT_ACCOUNTING
     */
    @Test
    void givenEnhancedAggregateAccountingMessage_WhenSettlementAccountingType_ThenVerifyNotProcessedButCheckpointed() throws IOException {
        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/enhanced/EnhancedAggregateAccountingSettlement.json")))) {
            CompositeModel jsonInputFile = objectMapper.readValue(isr, CompositeModel.class);

            Message<CompositeModel> message = MessageBuilder.withPayload(jsonInputFile).setHeader(CHECKPOINTER, checkpointer).build();
            serviceBusConfig.consumeServiceBusDetailPaymentMessage(aggregateService).accept(message);

            Mockito.verify(aggregateService, Mockito.times(0)).process(any(), any());
            Mockito.verify(checkpointer, Mockito.times(1)).success();
            Mockito.verify(checkpointer, Mockito.times(0)).failure();
        }
    }

    /***
     * Test Enhanced Aggregate Accounting Message with non-AGGREGATION messageTypeCode falls back to existing logic
     */
    @Test
    void givenEnhancedMessage_WhenNonAggregationMessageType_ThenVerifyFallbackToExistingLogic() throws IOException {
        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            CompositeModel jsonInputFile = objectMapper.readValue(isr, CompositeModel.class);
            CcReconHeader ccReconHeader = ModelExtractor.getCcReconHeader.apply(jsonInputFile).orElse(null);
            String sourceId = ModelExtractor.getSourceId.apply(jsonInputFile).orElse(null);

            Message<CompositeModel> message = MessageBuilder.withPayload(jsonInputFile).setHeader(CHECKPOINTER, checkpointer).build();
            serviceBusConfig.consumeServiceBusDetailPaymentMessage(aggregateService).accept(message);

            // Should fall back to existing EOF logic
            Mockito.verify(aggregateService, Mockito.times(1)).process(ccReconHeader, sourceId);
            Mockito.verify(checkpointer, Mockito.times(1)).success();
            Mockito.verify(checkpointer, Mockito.times(0)).failure();
        }
    }

}
