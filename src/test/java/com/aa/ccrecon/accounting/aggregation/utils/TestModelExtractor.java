package com.aa.ccrecon.accounting.aggregation.utils;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageEnvelope;
import com.aa.ccrecon.domain.aggregation.Payload;
import com.aa.ccrecon.domain.composite.header.CcReconHeader;
import com.aa.ccrecon.domain.composite.header.CcReconException;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

public class TestModelExtractor {

    private TestModelExtractor() {
    }

    public static final Function<AggregatedTransaction, Optional<AggregatedGeneralLedgerTransactionDetails>> getAggregatedGeneralLedgerTransactionDetails = aggregatedTransaction -> Optional.of(aggregatedTransaction)
            .map(AggregatedTransaction::getMessageEnvelope)
            .map(MessageEnvelope::getPayload)
            .map(Payload::getAggregatedGeneralLedgerTransactionDetails);

    public static final Function<AggregatedGeneralLedgerTransactionDetails, Optional<Instant>> getAggregatedTransactionTimestamp = aggregatedGeneralLedgerTransactionDetails -> Optional.of(aggregatedGeneralLedgerTransactionDetails)
            .map(AggregatedGeneralLedgerTransactionDetails::getDateTimestamp)
            .map(Instant::parse);

    public static final Function<AggregatedTransaction, Optional<List<CcReconException>>> getCcReconExceptions = aggregatedTransaction -> Optional.ofNullable(aggregatedTransaction)
            .map(AggregatedTransaction::getMessageEnvelope)
            .map(MessageEnvelope::getPayload)
            .map(Payload::getCcReconHeader)
            .map(CcReconHeader::getCcReconExceptions);

}
