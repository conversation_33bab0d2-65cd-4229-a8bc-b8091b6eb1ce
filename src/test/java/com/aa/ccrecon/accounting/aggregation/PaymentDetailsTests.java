package com.aa.ccrecon.accounting.aggregation;

import com.aa.ccrecon.accounting.aggregation.config.TestServiceBusConfig;
import com.aa.ccrecon.accounting.aggregation.utils.ModelExtractor;
import com.aa.ccrecon.accounting.aggregation.repository.TestAggregateGlReceivableAccountingTransRepository;
import com.aa.ccrecon.accounting.aggregation.repository.TestAggregateGlUatpReceivableAccountingTransRepository;
import com.aa.ccrecon.accounting.aggregation.utils.TestModelExtractor;
import com.aa.ccrecon.accounting.aggregation.utils.YamlPropertySourceFactory;
import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.composite.CompositeModel;
import com.aa.ccrecon.domain.composite.header.MessageContext;
import com.aa.ccrecon.domain.constants.SalesSource;
import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.itfacs.pmt.mask.Masked;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.itfacs.pmt.controller.StreamInputRequest;
import com.itfacs.pmt.controller.StreamOutputsResponse;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.json.JSONArray;
import org.json.JSONException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.cloud.stream.binder.test.EnableTestBinder;
import org.springframework.context.annotation.PropertySource;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;

import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.sql.Types;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.aa.ccrecon.domain.aggregation.MessageType.AGGREGATED_GL_NON_UATP;
import static com.aa.ccrecon.domain.aggregation.MessageType.AGGREGATED_GL_UATP;
import static com.aa.ccrecon.domain.subledger.TransactionType.TYPES.SALE;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@EnableTestBinder
@ContextConfiguration(classes = TestServiceBusConfig.class)
@ExtendWith({OutputCaptureExtension.class})
@PropertySource(value = "classpath:sql/TestQueries.yaml", factory = YamlPropertySourceFactory.class)
@Slf4j
public class PaymentDetailsTests extends TestContainerSetup {

    @Autowired
    private TestComponent testComponent;

    @Autowired
    private TestAggregateGlReceivableAccountingTransRepository testAggregateGlReceivableAccountingTransRepository;

    @Autowired
    private TestAggregateGlUatpReceivableAccountingTransRepository testAggregateGlUatpReceivableAccountingTransRepository;

    @Autowired
    NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Value("${GetCountOfAggregate}")
    private String getCountOfAggregate;

    @Value("${GetCountOfUatpAggregate}")
    private String getCountOfUatpAggregate;

    private static ObjectMapper objectMapper;

    private final Pattern uatpGlobalIdsPattern = Pattern.compile("UATP Global ID's assigned:\\s(.*)");

    private final Pattern globalIdsPattern = Pattern.compile("Aggregate complete with ([0-9]*) aggregated rows");

    @BeforeAll
    public static void init() {
        objectMapper = new ObjectMapper();
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
    }

    /***
     * END of file notice
     * @param output - Application output
     * @throws IOException - Processing exception
     */
    @Test
    @Sql(value = {"/sql/clean.sql", "/sql/aggregategl.sql"}, executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenEventStateEnd_WhenMessageReceivedAndProcessed_ThenVerifyAggregatedTransactionSent(CapturedOutput output) throws IOException, JSONException {
        try (InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            JSONObject jsonInputFile = objectMapper.readValue(isr, JSONObject.class);

            StreamOutputsResponse messages = testComponent.processEvent(new StreamInputRequest(1, 2, INCOMING_Q, List.of(EXCEPTION_Q, OUTGOING_Q), jsonInputFile));

            CompositeModel araFileMessage = objectMapper.readValue(jsonInputFile.toString(), CompositeModel.class);
            String sourceId = ModelExtractor.getSourceId.apply(araFileMessage).orElse(null);
            assertTrue(output.getOut().contains("New message received: '" + Masked.objectToMaskedString(araFileMessage) + "'"));
            assertTrue(output.getOut().contains("End of File message received. File: " + sourceId));

            validateGlobalIds(globalIdsPattern, AGGREGATED_GL_NON_UATP, SALE, output, messages);
            validateEmptyMessage(messages, 2);
        }
    }

    /***
     * END of file notice
     * @param output - Application output
     * @throws IOException - Processing exception
     */
    @Test
    @Sql(value = {"/sql/clean.sql", "/sql/aggregategl_uatp.sql"}, executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenEventStateEnd_WhenMessageReceivedAndProcessed_ThenVerifyUatpAggregatedTransactionSent(CapturedOutput output) throws IOException, JSONException {
        try (InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            JSONObject jsonInputFile = objectMapper.readValue(isr, JSONObject.class);

            StreamOutputsResponse messages = testComponent.processEvent(new StreamInputRequest(1, 2, INCOMING_Q, List.of(EXCEPTION_Q, OUTGOING_Q), jsonInputFile));

            CompositeModel araFileMessage = objectMapper.readValue(jsonInputFile.toString(), CompositeModel.class);
            String sourceId = ModelExtractor.getSourceId.apply(araFileMessage).orElse(null);
            assertTrue(output.getOut().contains("New message received: '" + Masked.objectToMaskedString(araFileMessage) + "'"));
            assertTrue(output.getOut().contains("End of File message received. File: " + sourceId));
            assertTrue(output.getOut().contains("UATP records selected and assigned Aggregate ID's: 20"));

            Matcher matcher = uatpGlobalIdsPattern.matcher(output.getOut());
            assertTrue(matcher.find());

            validateGlobalIds(uatpGlobalIdsPattern, AGGREGATED_GL_UATP, SALE, output, messages);
            validateEmptyMessage(messages, 2);
        }
    }

    /***
     * END of file notice
     * @param output - Application output
     * @throws IOException - Processing exception
     */
    @Test
    @Sql(value = {"/sql/clean.sql", "/sql/aggregategl.sql", "/sql/aggregategl_uatp.sql"}, executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenEventStateEnd_WhenMessageReceivedAndProcessed_ThenVerifyAllAggregatedTransactionSent(CapturedOutput output) throws IOException, JSONException {
        try (InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            JSONObject jsonInputFile = objectMapper.readValue(isr, JSONObject.class);

            StreamOutputsResponse messages = testComponent.processEvent(new StreamInputRequest(1, 4, INCOMING_Q, List.of(EXCEPTION_Q, OUTGOING_Q), jsonInputFile));

            CompositeModel araFileMessage = objectMapper.readValue(jsonInputFile.toString(), CompositeModel.class);
            String sourceId = ModelExtractor.getSourceId.apply(araFileMessage).orElse(null);
            assertTrue(output.getOut().contains("New message received: '" + Masked.objectToMaskedString(araFileMessage) + "'"));
            assertTrue(output.getOut().contains("End of File message received. File: " + sourceId));

            validateGlobalIds(globalIdsPattern, AGGREGATED_GL_NON_UATP, SALE, output, messages);
            validateGlobalIds(uatpGlobalIdsPattern, AGGREGATED_GL_UATP, SALE, output, messages);
            validateEmptyMessage(messages, 4);
        }
    }

    /***
     * Non END event states
     * @param filename - File to process
     * @param output - Application output
     * @throws IOException - Processing exception
     */
    @ParameterizedTest
    @CsvSource(
            {
                    "02 SampleContractARA-Trans.json",
                    "01 SampleContractARA-Begin.json"
            }
    )
    @Sql(value = "/sql/clean.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenEventStateNotEnd_WhenMessageReceivedAndProcessed_ThenVerifyAggregatedTransactionNotSent(String filename, CapturedOutput output) throws IOException {
        try (InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/" + filename)))) {
            JSONObject jsonInputFile = objectMapper.readValue(isr, JSONObject.class);

            StreamOutputsResponse messages = testComponent.processEvent(new StreamInputRequest(1, 1, INCOMING_Q, List.of(EXCEPTION_Q, OUTGOING_Q), jsonInputFile));

            CompositeModel araFileMessage = objectMapper.readValue(jsonInputFile.toString(), CompositeModel.class);
            String sourceId = ModelExtractor.getSourceId.apply(araFileMessage).orElse(null);
            MessageContext.MESSAGE_SUB_TYPE_CODE eventState = ModelExtractor.getMessageSubTypeCode.apply(araFileMessage).orElse(null);
            assertTrue(output.getOut().contains("New message received: '" + Masked.objectToMaskedString(araFileMessage) + "'"));
            assertTrue(output.getOut().contains("Invalid Event State. File: " + sourceId + ", Event State: " + eventState));
            assertFalse(output.getOut().contains("Going to add message to Sinks.Many."));

            validateEmptyMessage(messages, 0);
        }
    }

    private void validateGlobalIds(Pattern pattern, MessageType messageType, TransactionType.TYPES transType, CapturedOutput output, StreamOutputsResponse messages) throws JSONException {
        Matcher matcher = pattern.matcher(output.getOut());
        assertTrue(matcher.find());

        if (messageType == AGGREGATED_GL_UATP) {
            String match = matcher.group(1);
            JSONArray globalIds = new JSONArray(match);
            for (int i = 0; i < globalIds.length(); i++) {
                String globalId = globalIds.getString(i);
                validateGlobalIdMessage(globalId, getCountOfUatpAggregate, messageType, transType, output, messages);
            }
        } else {
            int match = Integer.parseInt(matcher.group(1));
            for (int i = 1; i <= match; i++) {
                validateGlobalIdMessage(String.valueOf(i), getCountOfAggregate, messageType, transType, output, messages);
            }
        }
    }

    private void validateGlobalIdMessage(String globalId, String sql, MessageType messageType, TransactionType.TYPES transType, CapturedOutput output, StreamOutputsResponse messages) {
        MapSqlParameterSource params = new MapSqlParameterSource().addValue("globalId", globalId, Types.VARCHAR);
        Integer count = namedParameterJdbcTemplate.queryForObject(sql, params, Integer.class);

        List<AggregatedTransaction> allMsgs = messages.getOutputs().entrySet().stream()
                .filter(e -> e.getKey().startsWith(OUTGOING_Q))
                .map(e -> (byte[]) e.getValue())
                .map(m -> {
                    try {
                        return objectMapper.readValue(m, AggregatedTransaction.class);
                    } catch (IOException e) {
                        fail("Error parsing message: " + e.getMessage());
                        return null;
                    }
                })
                .filter(atm -> {
                    var details = TestModelExtractor.getAggregatedGeneralLedgerTransactionDetails.apply(atm)
                            .orElse(new AggregatedGeneralLedgerTransactionDetails());
                    return details.getGlobalId().equals(globalId) &&
                            details.getCount() == count &&
                            details.getMessageType().equals(messageType) &&
                            details.getTransactionType().equals(transType) &&
                            Arrays.stream(SalesSource.values())
                                    .anyMatch(s -> s.equals(details.getSalesSource()));
                })
                .toList();

        assertThat(allMsgs.size(), is(1));
        AggregatedTransaction msg = allMsgs.getFirst();
        AggregatedGeneralLedgerTransactionDetails details = TestModelExtractor.getAggregatedGeneralLedgerTransactionDetails.apply(msg)
                .orElse(new AggregatedGeneralLedgerTransactionDetails());
        Instant aggregatedTransactionTimestamp = TestModelExtractor.getAggregatedTransactionTimestamp.apply(details)
                .orElse(null);
        if (msg != null && aggregatedTransactionTimestamp != null) {
            Instant now = Instant.now();
            assertTrue(output.getOut().contains("Going to add message to Sinks.Many. Message: '" + Masked.objectToMaskedString(msg) + "'"));
            assertTrue(aggregatedTransactionTimestamp.isAfter(now.minusSeconds(5)) && aggregatedTransactionTimestamp.isBefore(now));;
        } else {
            fail("AggregatedGeneralLedgerTransactionDetails or DateTimestamp is null");
        }

        validateConvertedFields(details);
    }

    private void validateConvertedFields(AggregatedGeneralLedgerTransactionDetails details) {
        switch (details.getMessageType()) {
            case AGGREGATED_GL_NON_UATP -> testAggregateGlReceivableAccountingTransRepository
                    .findByGlobalId(details.getGlobalId())
                    .ifPresentOrElse(entity -> validateEntity(entity.getCurrency(), entity.getConvertedCurrency(), entity.getSubTotal(), entity.getConvertedSubtotalAmount()),
                            () -> fail("Entity not found for globalId: " + details.getGlobalId()));
            case AGGREGATED_GL_UATP -> Optional.of(testAggregateGlUatpReceivableAccountingTransRepository.findAllByGlobalId(details.getGlobalId()))
                    .filter(list -> !list.isEmpty())
                    .ifPresentOrElse(
                        entities -> entities.forEach(entity ->
                            validateEntity(entity.getCurrency(), entity.getConvertedCurrency(), entity.getAmount(), entity.getConvertedAmount())),
                        () -> fail("No entities found for globalId: " + details.getGlobalId())
                    );
            default -> fail("Unexpected message type: " + details.getMessageType());
        }
    }

    private void validateEntity(String currency, String convertedCurrency, BigDecimal amount, BigDecimal convertedAmount) {
        assertAll(
                () -> assertNotNull(currency),
                () -> assertNotNull(convertedCurrency),
                () -> assertNotNull(amount),
                () -> assertNotNull(convertedAmount)
        );
        if (Objects.equals(currency, "USD")) {
            assertEquals(currency, convertedCurrency);
            assertEquals(amount.stripTrailingZeros(), convertedAmount.stripTrailingZeros());
        } else {
            assertNotEquals(currency, convertedCurrency);
            assertNotEquals(amount.stripTrailingZeros(), convertedAmount.stripTrailingZeros());
        }
    }

    /**
     *
     * @param messages
     * @param msgEndIndex 0-based index of the message that should NOT be there. Eg, if expecting 1 message, pass 1
     * @throws IOException
     */
    private void validateEmptyMessage(StreamOutputsResponse messages, int msgEndIndex) throws IOException {
        byte[] message = (byte[]) messages.getOutputs().get(OUTGOING_Q + msgEndIndex);
        if (message != null) {
            AggregatedTransaction aggregatedTransaction = objectMapper.readValue(message, AggregatedTransaction.class);
            AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = TestModelExtractor.getAggregatedGeneralLedgerTransactionDetails.apply(aggregatedTransaction).orElse(null);
            log.warn("Not-empty message: {}", aggregatedGeneralLedgerTransactionDetails);
        }
        assertNull(message);
    }
}
