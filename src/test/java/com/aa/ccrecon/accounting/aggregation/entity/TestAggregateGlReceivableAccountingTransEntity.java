package com.aa.ccrecon.accounting.aggregation.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;

import java.math.BigDecimal;

@Entity
@Getter
@Table(name = "aggregate_receivable_accounting_trans", schema = "rec_account")
public class TestAggregateGlReceivableAccountingTransEntity extends TestAggregateReceivableAccountingTransEntity {
    @Column(name = "currency")
    private String currency;

    @Column(name = "sub_total")
    private BigDecimal subTotal;

    @Column(name = "converted_currency")
    private String convertedCurrency;

    @Column(name = "converted_subtotal_amount")
    private BigDecimal convertedSubtotalAmount;
}
