package com.aa.ccrecon.accounting.aggregation.service;

import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;
import reactor.core.publisher.Sinks;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class MessageSenderServiceTests {

    @Mock
    Sinks.Many<Message<AggregatedTransaction>> serviceBusAggregatedTransactionSink;

    @Mock
    Sinks.Many<Message<AggregatedTransaction>> serviceBusExceptionSink;

    private static MessageSenderService messageSenderService;

    private static ObjectMapper objectMapper;

    @BeforeAll
    public static void init(){
        objectMapper = new ObjectMapper();
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
    }

    @BeforeEach
    public void setUp() {
        messageSenderService = new MessageSenderService(
                serviceBusAggregatedTransactionSink,
                serviceBusExceptionSink
        );
    }

    /***
     * Aggregated transaction send to service bus
     */
    @Test
    void givenValidAggregatedTransaction_WhenSentToGLQueue_ThenVerifySent() throws IOException {
        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1376572/AggregatedTransaction.json")))) {
            AggregatedTransaction aggregatedTransaction = objectMapper.readValue(isr, AggregatedTransaction.class);

            Assertions.assertNotNull(aggregatedTransaction);
            assertEquals(MessageType.AGGREGATED_GL_NON_UATP, aggregatedTransaction.getMessageEnvelope().getPayload().getAggregatedGeneralLedgerTransactionDetails().getMessageType());

            messageSenderService.sendMessageToGlQueue(aggregatedTransaction);

            verify(serviceBusAggregatedTransactionSink, times(1))
                    .emitNext(ArgumentMatchers.argThat(message -> message.getPayload().equals(aggregatedTransaction)),
                            argThat(handler -> handler.equals(Sinks.EmitFailureHandler.FAIL_FAST)));
        }
    }

    /***
     * Aggregated UATP transaction send to service bus
     */
    @Test
    void givenValidUATPAggregatedTransaction_WhenSentToGLQueue_ThenVerifySent() throws IOException {
        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1376572/AggregatedTransaction_UATP.json")))) {
            AggregatedTransaction aggregatedTransaction = objectMapper.readValue(isr, AggregatedTransaction.class);

            Assertions.assertNotNull(aggregatedTransaction);
            assertEquals(MessageType.AGGREGATED_GL_UATP, aggregatedTransaction.getMessageEnvelope().getPayload().getAggregatedGeneralLedgerTransactionDetails().getMessageType());

            messageSenderService.sendMessageToGlQueue(aggregatedTransaction);

            verify(serviceBusAggregatedTransactionSink, times(1))
                    .emitNext(ArgumentMatchers.argThat(message -> message.getPayload().equals(aggregatedTransaction)),
                            argThat(handler -> handler.equals(Sinks.EmitFailureHandler.FAIL_FAST)));
        }
    }
}
