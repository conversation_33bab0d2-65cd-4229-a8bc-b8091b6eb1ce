package com.aa.ccrecon.accounting.aggregation.repository;

import com.aa.ccrecon.accounting.aggregation.entity.TestAggregateGlUatpReceivableAccountingTransEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TestAggregateGlUatpReceivableAccountingTransRepository extends JpaRepository<TestAggregateGlUatpReceivableAccountingTransEntity, String> {
    List<TestAggregateGlUatpReceivableAccountingTransEntity> findAllByGlobalId(String globalId);
}
