package com.aa.ccrecon.accounting.aggregation.repository;

import com.aa.ccrecon.accounting.aggregation.entity.TestAggregateGlReceivableAccountingTransEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TestAggregateGlReceivableAccountingTransRepository extends JpaRepository<TestAggregateGlReceivableAccountingTransEntity, String> {
    Optional<TestAggregateGlReceivableAccountingTransEntity> findByGlobalId(String globalId);
}
