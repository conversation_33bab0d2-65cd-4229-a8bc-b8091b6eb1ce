package com.aa.ccrecon.accounting.aggregation.logger;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import com.aa.ccrecon.accounting.aggregation.entity.TestMaskEntity;
import com.aa.ccrecon.accounting.aggregation.factory.MaskingLoggerFactory;
import com.aa.itfacs.pmt.mask.Masked;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;

import java.time.Instant;
import java.time.LocalDateTime;

@ExtendWith({OutputCaptureExtension.class})
public class MaskingLoggerTests {

    @BeforeAll
    public static void onlyOnce() {
        Logger rootLogger = (Logger) LoggerFactory.getLogger("com.aa.ccrecon.accounting.aggregation");
        rootLogger.setLevel(Level.DEBUG);
    }

    static MaskingLogger logger = MaskingLoggerFactory.getLogger(MaskingLoggerTests.class);

    @Test
    void givenNullObject_WhenDebugLogged_ThenVerifyNullLogged(CapturedOutput output) {
        logger.info("Value to log: {}", (Object) null);

        Assertions.assertTrue(output.getOut().contains("Value to log: " + null));
    }

    @Test
    void givenNullObjects_WhenInfoLogged_ThenVerifyNullsLogged(CapturedOutput output) {
        logger.info("Values to log: {} {}", null, null);

        Assertions.assertTrue(output.getOut().contains("Values to log: " + null + " " + null));
    }

    @Test
    void givenObjectToMask_WhenInfoLogged_ThenVerifyNullsLogged(CapturedOutput output) {
        Instant instantNow = Instant.now();
        Instant instantLater = Instant.now().plusSeconds(300);
        TestMaskEntity entity = new TestMaskEntity(instantNow, instantLater);

        logger.info("Value to log: {}", entity);

        Assertions.assertTrue(output.getOut().contains("Value to log: " + Masked.objectToMaskedString(entity)));
    }

    @ParameterizedTest
    @CsvSource(
            {
                    "debug",
                    "info",
                    "warn",
                    "error"
            }
    )
    void givenObjectToMask_WhenLogged_ThenVerifyMasked(String level, CapturedOutput output) {
        Instant instantNow = Instant.now();
        Instant instantLater = Instant.now().plusSeconds(300);
        TestMaskEntity entity1 = new TestMaskEntity(instantNow, instantLater);

        String message = "Value to log: {}";

        switch (level) {
            case "debug":
                logger.debug(message, entity1);
                break;
            case "info":
                logger.info(message, entity1);
                break;
            case "warn":
                logger.warn(message, entity1);
                break;
            case "error":
                logger.error(message, entity1);
                break;
        }

        Assertions.assertTrue(output.getOut().contains("Value to log: " + Masked.objectToMaskedString(entity1)));
        Assertions.assertFalse(output.getOut().contains(instantNow.toString()));
        Assertions.assertTrue(output.getOut().contains(instantLater.toString()));
    }

    @ParameterizedTest
    @CsvSource(
            {
                    "debug",
                    "info",
                    "warn",
                    "error"
            }
    )
    void givenObjectsToMask_WhenLogged_ThenVerifyMasked(String level, CapturedOutput output) {
        Instant instantNow = Instant.now();
        Instant instantLater = Instant.now().plusSeconds(300);
        TestMaskEntity entity1 = new TestMaskEntity(instantNow, instantLater);

        LocalDateTime localDateTimeNow = LocalDateTime.now();
        LocalDateTime localDateTimeLater = LocalDateTime.now().plusSeconds(300);
        TestMaskEntity entity2 = new TestMaskEntity(localDateTimeNow, localDateTimeLater);

        String message = "Values to log: {} {}";

        switch (level) {
            case "debug":
                logger.debug(message, entity1, entity2);
                break;
            case "info":
                logger.info(message, entity1, entity2);
                break;
            case "warn":
                logger.warn(message, entity1, entity2);
                break;
            case "error":
                logger.error(message, entity1, entity2);
                break;
        }

        Assertions.assertTrue(output.getOut().contains("Values to log: " + Masked.objectToMaskedString(entity1) + " " + Masked.objectToMaskedString(entity2)));
        Assertions.assertFalse(output.getOut().contains(instantNow.toString()));
        Assertions.assertFalse(output.getOut().contains(localDateTimeNow.toString()));
        Assertions.assertTrue(output.getOut().contains(instantLater.toString()));
        Assertions.assertTrue(output.getOut().contains(localDateTimeLater.toString()));
    }

}
