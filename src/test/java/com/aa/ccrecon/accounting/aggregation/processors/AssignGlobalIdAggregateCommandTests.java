package com.aa.ccrecon.accounting.aggregation.processors;

import com.aa.ccrecon.accounting.aggregation.mapper.IntegerResultRowMapper;
import com.aa.ccrecon.accounting.aggregation.repository.AggregateGlReceivableAccountingTransRepository;
import com.aa.ccrecon.accounting.aggregation.repository.AggregateGlUatpReceivableAccountingTransRepository;
import com.aa.ccrecon.accounting.aggregation.utils.YamlPropertySourceFactory;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.PropertySource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.transaction.PlatformTransactionManager;

import static org.mockito.ArgumentMatchers.*;

@SpringBootTest
@EnableAutoConfiguration(exclude = {DataSourceAutoConfiguration.class})
@PropertySource(value= "classpath:sql/NON_UATPAggregateQuery.yaml", factory = YamlPropertySourceFactory.class)
public class AssignGlobalIdAggregateCommandTests {

	@MockBean
	JdbcTemplate jdbcTemplate;

	@MockBean
	NamedParameterJdbcTemplate namedParameterJdbcTemplate;

	@MockBean
	AggregateGlReceivableAccountingTransRepository aggregateGlReceivableAccountingTransRepository;

	@MockBean
	AggregateGlUatpReceivableAccountingTransRepository aggregateGlUatpReceivableAccountingTransRepository;

	@MockBean
	PlatformTransactionManager platformTransactionManager;

	@Value("${AssignGlobalId}")
	private String sql;

	@SpyBean
	AssignGlobalIdAggregateCommand assignGlobalId;
	
	@Test
	@DisplayName("call jdbc template execute method with provided maxBatchSize")
	public void givenBatchSize_WhenAssigningGlobalIds_VerifyDatabaseCall() {
		assignGlobalId.execute(new AggregateRequest());

		Mockito.verify(namedParameterJdbcTemplate, Mockito.times(1))
				.query(eq(sql), any(MapSqlParameterSource.class), any(IntegerResultRowMapper.class));
	}

}
