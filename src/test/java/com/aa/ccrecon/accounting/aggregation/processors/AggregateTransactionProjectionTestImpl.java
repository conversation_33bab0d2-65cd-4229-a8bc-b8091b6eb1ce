package com.aa.ccrecon.accounting.aggregation.processors;

import com.aa.ccrecon.accounting.aggregation.mapper.AggregateTransactionProjection;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.constants.SalesSource;
import com.aa.ccrecon.domain.subledger.TransactionType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class AggregateTransactionProjectionTestImpl implements AggregateTransactionProjection {

	private final String globalId;

	private final Long count;

	private final MessageType messageType;

	private final SalesSource salesSource;

	private final TransactionType.TYPES transactionType;
}