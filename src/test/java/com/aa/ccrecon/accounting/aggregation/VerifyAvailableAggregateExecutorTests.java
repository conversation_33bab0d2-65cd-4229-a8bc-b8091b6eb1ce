package com.aa.ccrecon.accounting.aggregation;


import com.aa.ccrecon.accounting.aggregation.mapper.StringResultRowMapper;
import com.aa.ccrecon.accounting.aggregation.processors.*;
import com.aa.ccrecon.accounting.aggregation.processors.uatp.UATPAggregateDataCommand;
import com.aa.ccrecon.accounting.aggregation.processors.uatp.UATPAssignGlobalIdCommand;
import com.aa.ccrecon.accounting.aggregation.processors.uatp.UATPCreationCommand;
import com.aa.ccrecon.accounting.aggregation.service.AggregateService;
import com.aa.ccrecon.accounting.aggregation.utils.ModelExtractor;
import com.aa.ccrecon.accounting.aggregation.utils.YamlPropertySourceFactory;
import com.aa.ccrecon.domain.composite.CompositeModel;
import com.aa.ccrecon.domain.composite.header.CcReconHeader;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.context.annotation.PropertySource;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Objects;

import static org.mockito.Mockito.*;

@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@ExtendWith({OutputCaptureExtension.class, MockitoExtension.class})
@PropertySource(value= "classpath:sql/NON_UATPAggregateQuery.yaml", factory = YamlPropertySourceFactory.class)
@PropertySource(value="classpath:sql/UATPAggregateQueries.yaml", factory = YamlPropertySourceFactory.class)
@PropertySource(value="classpath:sql/TestQueries.yaml", factory = YamlPropertySourceFactory.class)
public class VerifyAvailableAggregateExecutorTests extends TestContainerSetup {

    @Autowired
    @InjectMocks
    VerifyAvailableAggregateExecutor verifyAvailableAggregateExecutor;

    @Autowired
    @InjectMocks
    ImportAggregateCommand importAggregateCommand;

    @Autowired
    @InjectMocks
    AssignGlobalIdAggregateCommand assignGlobalIdAggregateCommand;

    @Autowired
    @InjectMocks
    UATPAggregateDataCommand uatpAggregateDataCommand;

    @Autowired
    @InjectMocks
    UATPAssignGlobalIdCommand uatpAssignGlobalIdCommand;

    @Autowired
    @InjectMocks
    UATPCreationCommand uatpCreationCommand;

    @Autowired
    AggregateExecutor aggregateGlExecutor;

    @Autowired
    AggregateExecutor aggregateGlUatpExecutor;

    @Autowired
    AggregateTransactionQueryAndSenderCommand aggregateTransactionQueryAndSenderCommand;


    @SpyBean
    JdbcTemplate jdbcTemplate;

    @SpyBean
    NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    AggregateService aggregateService;

    @Value("${VerifyAvailableAggregateGl}")
    private String verifyAvailableAggregateGl;

    @Value("${ImportAggregate}")
    private String importAggregate;

    @Value("${GetAllTransDetails}")
    private String getAllTransDetails;

    @Value("${UATPAggregate_aggregateData1}")
    private String uatpAggregateAggregateData1;

    @Value("${UATPAggregate_aggregateData2}")
    private String uatpAggregateAggregateData2;

    @Value("${UATPAggregate_aggregateData3}")
    private String uatpAggregateAggregateData3;

    @Value("${UATPAggregate_assignGlobalId_1}")
    private String assignGlobalId1;

    @Value("${UATPAggregate_assignGlobalId_2}")
    private String assignGlobalId2;

    @Value("${UATPAggregate_assignGlobalId_3}")
    private String assignGlobalId3;

    @Value("${UATPAggregate_assignGlobalId_4}")
    private String assignGlobalId4;

    @Value("${UATPAggregate_assignGlobalId_5}")
    private String assignGlobalId5;

    @Value("${UATPAggregate_getGlobalIds}")
    private String getGlobalIds;

    private static ObjectMapper objectMapper;

    @BeforeAll
    public static void init(){
        objectMapper = new ObjectMapper();
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
    }

    @BeforeEach
    public void setUp(){
        aggregateService = new AggregateService(verifyAvailableAggregateExecutor, importAggregateCommand,
                assignGlobalIdAggregateCommand, aggregateTransactionQueryAndSenderCommand, uatpAggregateDataCommand,
                uatpAssignGlobalIdCommand, uatpCreationCommand, aggregateGlExecutor, aggregateGlUatpExecutor);
    }

    /***
     * Mixed Transaction Details Aggregation
     * @param output - Application output
     */
    @Test
    @Sql(value = {"/sql/clean.sql", "/sql/aggregategl.sql", "/sql/aggregategl_uatp.sql"}, executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenMixedTransDetailsSampleData_WhenAggregateServiceProcessed_ThenVerifyAggregatedTransactionCount(CapturedOutput output) throws IOException {
        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            CompositeModel jsonInputFile = objectMapper.readValue(isr, CompositeModel.class);
            CcReconHeader ccReconHeader = ModelExtractor.getCcReconHeader.apply(jsonInputFile).orElse(null);
            String sourceId = ModelExtractor.getSourceId.apply(jsonInputFile).orElse(null);

            verifyTableData(20, 40);

            aggregateService.process(ccReconHeader, sourceId);

            Mockito.verify(jdbcTemplate, times(1))
                    .queryForObject(verifyAvailableAggregateGl, Integer.class);
            Mockito.verify(namedParameterJdbcTemplate, times(1))
                    .update(eq(uatpAggregateAggregateData1), any(MapSqlParameterSource.class));
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(uatpAggregateAggregateData2);
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(uatpAggregateAggregateData3);
            Mockito.verify(namedParameterJdbcTemplate, times(1))
                    .update(eq(assignGlobalId1), any(MapSqlParameterSource.class));
            Mockito.verify(namedParameterJdbcTemplate, times(1))
                    .update(eq(assignGlobalId2), any(MapSqlParameterSource.class));
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(assignGlobalId3);
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(assignGlobalId4);
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(assignGlobalId5);
            Mockito.verify(jdbcTemplate, times(1))
                    .query(ArgumentMatchers.eq(getGlobalIds), ArgumentMatchers.any(StringResultRowMapper.class));

            assert (output.getOut().contains("Found rows to aggregate 20"));
            assert (output.getOut().contains("Aggregate complete with 2 aggregated rows"));
            assert (output.getOut().contains("UATP records selected and assigned Aggregate ID's: 20"));
            assert (output.getOut().contains("UATP Global ID's assigned: [UATP-3, UATP-4]"));
        }
    }

    /***
     * Empty SQL Table
     * @param output - Application output
     */
    @Test
    @Sql(value = "/sql/clean.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenNoTransDetailsSampleData_WhenAggregateServiceProcessed_ThenVerifyNoAggregatedTransactionCount(CapturedOutput output) throws IOException {
        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            CompositeModel jsonInputFile = objectMapper.readValue(isr, CompositeModel.class);
            CcReconHeader ccReconHeader = ModelExtractor.getCcReconHeader.apply(jsonInputFile).orElse(null);
            String sourceId = ModelExtractor.getSourceId.apply(jsonInputFile).orElse(null);

            verifyTableData(0, 0);

            aggregateService.process(ccReconHeader, sourceId);

            Mockito.verify(jdbcTemplate, times(1))
                    .queryForObject(verifyAvailableAggregateGl, Integer.class);
            Mockito.verify(namedParameterJdbcTemplate, times(1))
                    .update(eq(uatpAggregateAggregateData1), any(MapSqlParameterSource.class));
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(uatpAggregateAggregateData2);
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(uatpAggregateAggregateData3);
            Mockito.verify(namedParameterJdbcTemplate, times(1))
                    .update(eq(assignGlobalId1), any(MapSqlParameterSource.class));
            Mockito.verify(namedParameterJdbcTemplate, times(1))
                    .update(eq(assignGlobalId2), any(MapSqlParameterSource.class));
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(assignGlobalId3);
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(assignGlobalId4);
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(assignGlobalId5);
            Mockito.verify(jdbcTemplate, times(1))
                    .query(ArgumentMatchers.eq(getGlobalIds), ArgumentMatchers.any(StringResultRowMapper.class));

            assert (output.getOut().contains("Found rows to aggregate 0"));
            assert (output.getOut().contains("Aggregate complete with 0 aggregated rows"));
            assert (output.getOut().contains("UATP records selected and assigned Aggregate ID's: 0"));
            assert (output.getOut().contains("UATP Global ID's assigned: []"));
        }
    }

    /***
     * No Valid Aggregate Transactions
     * @param output - Application output
     */
    @Test
    @Sql(value = {"/sql/clean.sql", "/sql/aggregategl_uatp.sql"}, executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenAggregateGlUatpTransDetailsSampleData_WhenAggregateServiceProcessed_ThenVerifyNoAggregatedTransactionCount(CapturedOutput output) throws IOException {
        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            CompositeModel jsonInputFile = objectMapper.readValue(isr, CompositeModel.class);
            CcReconHeader ccReconHeader = ModelExtractor.getCcReconHeader.apply(jsonInputFile).orElse(null);
            String sourceId = ModelExtractor.getSourceId.apply(jsonInputFile).orElse(null);

            verifyTableData(20, 20);

            aggregateService.process(ccReconHeader, sourceId);

            Mockito.verify(jdbcTemplate, times(1))
                    .queryForObject(verifyAvailableAggregateGl, Integer.class);
            Mockito.verify(namedParameterJdbcTemplate, times(1))
                    .update(eq(uatpAggregateAggregateData1), any(MapSqlParameterSource.class));
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(uatpAggregateAggregateData2);
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(uatpAggregateAggregateData3);
            Mockito.verify(namedParameterJdbcTemplate, times(1))
                    .update(eq(assignGlobalId1), any(MapSqlParameterSource.class));
            Mockito.verify(namedParameterJdbcTemplate, times(1))
                    .update(eq(assignGlobalId2), any(MapSqlParameterSource.class));
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(assignGlobalId3);
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(assignGlobalId4);
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(assignGlobalId5);
            Mockito.verify(jdbcTemplate, times(1))
                    .query(ArgumentMatchers.eq(getGlobalIds), ArgumentMatchers.any(StringResultRowMapper.class));

            assert (output.getOut().contains("Found rows to aggregate 0"));
            assert (output.getOut().contains("UATP records selected and assigned Aggregate ID's: 20"));
            assert (output.getOut().contains("UATP Global ID's assigned: [UATP-1, UATP-2]"));
        }
    }

    /***
     * Only Valid Aggregate Transactions
     * @param output - Application output
     */
    @Test
    @Sql(value = {"/sql/clean.sql", "/sql/aggregategl.sql"}, executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenAggregateGlTransDetailsSampleData_WhenAggregateServiceProcessed_ThenVerifyAggregatedTransactionCount(CapturedOutput output) throws IOException {
        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            CompositeModel jsonInputFile = objectMapper.readValue(isr, CompositeModel.class);
            CcReconHeader ccReconHeader = ModelExtractor.getCcReconHeader.apply(jsonInputFile).orElse(null);
            String sourceId = ModelExtractor.getSourceId.apply(jsonInputFile).orElse(null);

            verifyTableData(0, 20);

            aggregateService.process(ccReconHeader, sourceId);

            Mockito.verify(jdbcTemplate, times(1))
                    .queryForObject(verifyAvailableAggregateGl, Integer.class);
            Mockito.verify(namedParameterJdbcTemplate, times(1))
                    .update(eq(uatpAggregateAggregateData1), any(MapSqlParameterSource.class));
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(uatpAggregateAggregateData2);
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(uatpAggregateAggregateData3);
            Mockito.verify(namedParameterJdbcTemplate, times(1))
                    .update(eq(assignGlobalId1), any(MapSqlParameterSource.class));
            Mockito.verify(namedParameterJdbcTemplate, times(1))
                    .update(eq(assignGlobalId2), any(MapSqlParameterSource.class));
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(assignGlobalId3);
            Mockito.verify(jdbcTemplate, times(1))
                    .execute(assignGlobalId4);
            Mockito.verify(jdbcTemplate, times(1))
                    .query(ArgumentMatchers.eq(getGlobalIds), ArgumentMatchers.any(StringResultRowMapper.class));

            assert (output.getOut().contains("Found rows to aggregate 20"));
            assert (output.getOut().contains("Aggregate complete with 2 aggregated rows"));
            assert (output.getOut().contains("UATP records selected and assigned Aggregate ID's: 0"));
            assert (output.getOut().contains("UATP Global ID's assigned: []"));
        }
    }

    /***
     * Validate EmptyResultDataAccessException is captured
     * @param output - Application output
     */
    @Test
    @Sql(value = {"/sql/clean.sql", "/sql/aggregategl.sql", "/sql/aggregategl_uatp.sql"}, executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void givenMixedTransDetailsSampleData_WhenAggregateServiceError_ThenVerifyAggregatedNoTransactionCount(CapturedOutput output) throws IOException {
        try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
            CompositeModel jsonInputFile = objectMapper.readValue(isr, CompositeModel.class);
            CcReconHeader ccReconHeader = ModelExtractor.getCcReconHeader.apply(jsonInputFile).orElse(null);
            String sourceId = ModelExtractor.getSourceId.apply(jsonInputFile).orElse(null);

            EmptyResultDataAccessException exception = Mockito.mock(EmptyResultDataAccessException.class, Mockito.withSettings()
                    .useConstructor("Mock exception", 10)
                    .defaultAnswer(Mockito.CALLS_REAL_METHODS)
            );

            verifyTableData(20, 40);

            doThrow(exception).when(jdbcTemplate)
                    .queryForObject(verifyAvailableAggregateGl, Integer.class);
            doThrow(exception).when(jdbcTemplate)
                    .queryForObject(importAggregate, Integer.class);

            aggregateService.process(ccReconHeader, sourceId);

            Mockito.verify(jdbcTemplate, times(1))
                    .queryForObject(verifyAvailableAggregateGl, Integer.class);
        }
    }

    void verifyTableData(Integer expectedOaTpRows, Integer expectedTotalRows) {
        Integer oaTpRows = jdbcTemplate.queryForObject(verifyAvailableAggregateGl, Integer.class);
        Assertions.assertEquals(expectedOaTpRows, oaTpRows);

        Integer totalRows = jdbcTemplate.queryForObject(getAllTransDetails, Integer.class);
        Assertions.assertEquals(expectedTotalRows, totalRows);
    }
}
