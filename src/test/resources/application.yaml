server:
  port: 8080

spring:
  main:
    allow-bean-definition-overriding: true
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: https://idp.aa.com/ext/reg

  cloud:
    stream:
      default:
        producer:
          error-channel-enabled: true
    function:
      definition: consumeServiceBusDetailPaymentMessage;supplyServiceBusAggregatedTransactionMessage;supplyServiceBusException;

  datasource:
    url: ***********************************
    username: test
    password: test
    driverClassName: org.postgresql.Driver
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    show-sql: true
    hibernate:
      ddl-auto: none

  jackson:
    serialization:
      fail-on-empty-beans: false

# uncomment this line to disable authentication
authenticate: false

messaging:
  sbqConnectionString: Endpoint=sb://placeholder.net/;SharedAccessKeyName=placeholder;SharedAccessKey=placeholder
  accountingGlQueue: placeholder
  
retry:
  delay: 1
  times: 2

ccrecon:
  aggregate:
    max-batch-size: 999

logging:
  level:
    com.aa.ccrecon.accounting.aggregation: DEBUG