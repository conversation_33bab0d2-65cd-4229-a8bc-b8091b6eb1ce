{"messageEnvelope": {"messageHeader": {"headerVersion": "1.1.0", "timestamp": "2000-10-31T01:30:00.000-05:00", "sourceContext": {"sourceId": "itfacs-pmt-ccrecon-aggregation-accounting", "applicationId": "ccrecon", "transactionId": "60dc638e-2c0e-43a0-b51b-62e1636d0bca"}, "messageContext": {"messageContentType": "application/json", "messageContentEncoding": "UTF-8"}}, "augmentationPoint": {}, "payload": {"ccReconHeader": {"messageEventRescheduleCount": "0", "ccReconExceptions": [{"exceptionCode": "", "exceptionSource": "", "exceptionStatus": ""}]}, "aggregatedGeneralLedgerTransactionDetails": {"dateTimestamp": "2024-03-23T23:32:11+0000", "globalId": "566", "count": "400", "messageType": "AGGREGATED_GL_UATP"}}}}