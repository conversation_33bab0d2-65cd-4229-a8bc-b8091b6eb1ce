VerifyTransDetails: |
  select count(*) 
      FROM rec_account.receivable_accounting_trans_details 
      WHERE aggregate_id IS NOT NULL 
      AND is_processed = TRUE

VerifyAvailableAggregateGl: |
  select count(*) 
      FROM rec_account.receivable_accounting_trans_details 
      WHERE is_processed = FALSE 
      AND post_to_ledger IS NOT NULL AND post_to_ledger <> '' 
      AND payment_type IN ('TP', 'OA') 
      AND created_timestamp <= CURRENT_TIMESTAMP  

GetAllTransDetails: |
  select count(*) 
      FROM rec_account.receivable_accounting_trans_details

GetCountOfAggregate: |
  select count(*) 
      FROM rec_account.aggregate_receivable_accounting_trans
      WHERE global_id = :globalId

GetCountOfUatpAggregate: |
  select count(*) 
      FROM rec_account.uatp_aggregate_receivable_accounting_trans
      WHERE global_id = :globalId