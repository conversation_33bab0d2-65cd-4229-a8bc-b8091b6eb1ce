CREATE SCHEMA IF NOT EXISTS rec_account;

create table rec_account.aggregate_receivable_accounting_trans (
    aggregate_id int NOT NULL,
    recon_id            varchar(255),
    post_to_ledger      varchar(16),
    currency            varchar(100),
    debit_company_code  varchar(50),
    debit_account       varchar(50),
    credit_account      varchar(50),
    line_item_text      varchar(255),
    sub_total           numeric(10, 2),
    is_processed        boolean default false,
    global_id           text,
    batch_number        integer,
    created_timestamp   timestamp(6) with time zone default clock_timestamp() not null,
    sales_source        varchar(50),
    credit_company_code varchar(50),
    transaction_type varchar(50),
    converted_currency character varying(10),
    converted_subtotal_amount numeric(15,3),
    constraint aggregate_receivable_accounting_id_pkey primary key (aggregate_id)
);

CREATE SEQUENCE IF NOT EXISTS rec_account.receivable_accounting_trans_details_detail_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE **********
    CACHE 1;

CREATE TABLE rec_account.receivable_accounting_trans_details (
    detail_id integer NOT NULL DEFAULT nextval('rec_account.receivable_accounting_trans_details_detail_id_seq'::regclass),
    document_uuid character varying(100),
    client_capture_received_id uuid,
    psp_reference character varying(255),
    reference_id character varying(200),
    recon_id character varying(255),
    post_to_ledger character varying(16),
    aggregate_id text,
    pnr character varying(50),
    ticket_number character varying(50),
    amount numeric(15,3),
    payment_type character varying(50),
    processor character varying(50),
    sales_channel character varying(50),
    transaction_type character varying(50),
    currency character varying(10),
    debit_company_code character varying(50),
    cost_center character varying(50),
    merchant_id character varying(50),
    debit_account character varying(50),
    credit_account character varying(50),
    line_item_text character varying(255),
    is_processed boolean DEFAULT false,
    transaction_date date,
    issue_date date,
    created_timestamp timestamp(6) with time zone NOT NULL DEFAULT clock_timestamp(),
    payment_processing_path character varying,
    sales_source character varying(50),
    credit_company_code character varying(50),
    card_bin character varying(6),
    card_last_four_digits character varying(4),
    country_code character varying(2),
    fop_channel character varying(50),
    station_number character varying(100),
    refund_id character varying(200),
    converted_currency character varying(10),
    converted_amount numeric(15,3),
    CONSTRAINT receivable_accounting_trans_details_id_pkey PRIMARY KEY (detail_id)
);

CREATE SEQUENCE IF NOT EXISTS rec_account.uatp_aggregate_receivable_accounting_trans_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE TABLE rec_account.uatp_aggregate_receivable_accounting_trans (
    id integer NOT NULL DEFAULT nextval('rec_account.uatp_aggregate_receivable_accounting_trans_id_seq'::regclass),
    aggregate_id text,
    recon_id character varying(255),
    post_to_ledger character varying(16),
    currency character varying(20),
    debit_company_code character varying(50),
    debit_account character varying(50),
    credit_account character varying(50),
    line_item_text character varying(255),
    sort_order integer,
    amount numeric(15,3),
    record_type character varying(30),
    is_processed boolean DEFAULT false,
    global_id text,
    batch_number integer,
    created_timestamp timestamp(6) with time zone NOT NULL DEFAULT clock_timestamp(),
    detail_id integer,
    sales_source character varying(50),
    credit_company_code character varying(50),
    transaction_type character varying(50),
    converted_currency character varying(10),
    converted_amount numeric(15,3),
    CONSTRAINT uatp_aggregate_receivable_accounting_id_pkey PRIMARY KEY (id)
);

CREATE SEQUENCE IF NOT EXISTS rec_account.serial_aggregate_id
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;


CREATE SEQUENCE IF NOT EXISTS rec_account.serial_uatp_global_id
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;


CREATE SEQUENCE IF NOT EXISTS rec_account.serial_global_id
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;