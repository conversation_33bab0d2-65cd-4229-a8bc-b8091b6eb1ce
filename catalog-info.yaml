apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: 'itfacs-pmt-ccrecon-aggregation-accounting'
  description: 'itfacs-pmt-ccrecon-aggregation-accounting'
  annotations:
    github.com/project-slug: 'AAInternal/itfacs-pmt-ccrecon-aggregation-accounting'
    argocd/app-selector: 'backstage-name=itfacs-pmt-ccrecon-aggregation-accounting'
    backstage.io/kubernetes-label-selector: 'backstage.io/kubernetes-id=itfacs-pmt-ccrecon-aggregation-accounting'
  tags:
    - java
    - spring-api
spec:
  type: service
  lifecycle: experimental
  owner: "payments-processing-system"
  providesApis:
    - 'itfacs-pmt-ccrecon-aggregation-accounting-api'
